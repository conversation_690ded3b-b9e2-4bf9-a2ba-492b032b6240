create table customer_attribute_answer
(
    customer_attribute_no          numeric(8,0) not null,
    customer_attribute_choices_no  numeric(8,0) not null,
    customer_code                  varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_attribute_answer_pk primary key (customer_code, customer_attribute_no, customer_attribute_choices_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_attribute_answer is '顧客属性回答';

comment on column customer_attribute_answer.customer_attribute_no is '顧客属性番号';

comment on column customer_attribute_answer.customer_attribute_choices_no is '顧客属性選択肢番号';

comment on column customer_attribute_answer.customer_code is '顧客コード';

comment on column customer_attribute_answer.orm_rowid is 'データ行ID';

comment on column customer_attribute_answer.created_user is '作成ユーザ';

comment on column customer_attribute_answer.created_datetime is '作成日時';

comment on column customer_attribute_answer.updated_user is '更新ユーザ';

comment on column customer_attribute_answer.updated_datetime is '更新日時';

create unique index customer_attribute_answer_ix0
on customer_attribute_answer
(orm_rowid)
tablespace ts_customer_i01;
