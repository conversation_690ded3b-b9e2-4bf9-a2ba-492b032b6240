create table regular_sale_base
(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    regular_cycle_kind_list        varchar(100),
    regular_cycle_days_list        varchar(100),
    regular_cycle_months_list      varchar(100),
    regular_sale_stop_from         numeric(5,0),
    regular_sale_stop_to           numeric(5,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_base_pk primary key (shop_code, regular_sale_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table regular_sale_base is '定期便基本情報';

comment on column regular_sale_base.shop_code is 'ショップコード';

comment on column regular_sale_base.regular_sale_code is '定期便コード';

comment on column regular_sale_base.sku_code is 'SKUコード';

comment on column regular_sale_base.commodity_code is '商品コード';

comment on column regular_sale_base.regular_cycle_kind_list is '定期サイクル種別指定';

comment on column regular_sale_base.regular_cycle_days_list is '定期サイクル日付間隔指定';

comment on column regular_sale_base.regular_cycle_months_list is '定期サイクル○ヶ月間隔指定';

comment on column regular_sale_base.regular_sale_stop_from is '定期休止回次FROM';

comment on column regular_sale_base.regular_sale_stop_to is '定期休止回次TO';

comment on column regular_sale_base.orm_rowid is 'データ行ID';

comment on column regular_sale_base.created_user is '作成ユーザ';

comment on column regular_sale_base.created_datetime is '作成日時';

comment on column regular_sale_base.updated_user is '更新ユーザ';

comment on column regular_sale_base.updated_datetime is '更新日時';

create unique index regular_sale_base_ix0
on regular_sale_base
(orm_rowid)
tablespace ts_commodity_i01;
