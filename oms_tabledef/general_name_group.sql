create table general_name_group
(
    shop_code                      varchar(16) not null,
    general_name_group_code        varchar(30) not null,
    general_name_group_name        varchar(100) not null,
    display_order                  numeric(8,0) not null,
    maintenance_enabled_flg        numeric(1,0) not null,
    register_enabled_flg           numeric(1,0) not null,
    delete_enabled_flg             numeric(1,0) not null,
    string01_description           varchar(1000),
    string02_description           varchar(1000),
    string03_description           varchar(1000),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint general_name_group_pk primary key (shop_code, general_name_group_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table general_name_group is '汎用名称グループ';

comment on column general_name_group.shop_code is 'ショップコード';

comment on column general_name_group.general_name_group_code is '汎用グループコード';

comment on column general_name_group.general_name_group_name is '汎用名称グループ名称';

comment on column general_name_group.display_order is '表示順';

comment on column general_name_group.maintenance_enabled_flg is 'メンテナンス可能フラグ';

comment on column general_name_group.register_enabled_flg is '登録可能フラグ';

comment on column general_name_group.delete_enabled_flg is '削除可能フラグ';

comment on column general_name_group.string01_description is '文字列01説明';

comment on column general_name_group.string02_description is '文字列02説明';

comment on column general_name_group.string03_description is '文字列03説明';

comment on column general_name_group.orm_rowid is 'データ行ID';

comment on column general_name_group.created_user is '作成ユーザ';

comment on column general_name_group.created_datetime is '作成日時';

comment on column general_name_group.updated_user is '更新ユーザ';

comment on column general_name_group.updated_datetime is '更新日時';

create unique index general_name_group_ix0
on general_name_group
(orm_rowid)
tablespace ts_shop_i01;
