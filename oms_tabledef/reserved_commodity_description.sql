create table reserved_commodity_description
(
    shop_code                      varchar(16) not null,
    reflect_datetime_code          varchar(10) not null,
    commodity_code                 varchar(16) not null,
    commodity_description_type     numeric(2,0) not null,
    description_content            text,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint res_commodity_description_pk primary key (shop_code, reflect_datetime_code, commodity_code, commodity_description_type) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table reserved_commodity_description is '商品情報変更予約商品説明';

comment on column reserved_commodity_description.shop_code is 'ショップコード';

comment on column reserved_commodity_description.reflect_datetime_code is '反映日時コード';

comment on column reserved_commodity_description.commodity_code is '商品コード';

comment on column reserved_commodity_description.commodity_description_type is '商品説明区分';

comment on column reserved_commodity_description.description_content is '説明本文';

comment on column reserved_commodity_description.orm_rowid is 'データ行ID';

comment on column reserved_commodity_description.created_user is '作成ユーザ';

comment on column reserved_commodity_description.created_datetime is '作成日時';

comment on column reserved_commodity_description.updated_user is '更新ユーザ';

comment on column reserved_commodity_description.updated_datetime is '更新日時';

create unique index res_commodity_description_ix0
on reserved_commodity_description
(orm_rowid)
tablespace ts_commodity_i01;
