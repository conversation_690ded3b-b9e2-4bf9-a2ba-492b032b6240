create table yamato_sv_level
(
    depature_base_store            varchar(3) not null,
    sorting_cd                     varchar(7) not null,
    service_level                  varchar(2),
    fastest_delivery_time_kbn      varchar(2),
    start_date                     timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint yamato_sv_level_pk primary key (depature_base_store, sorting_cd) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table yamato_sv_level is 'ヤマト時間帯判定サービスレベルマスタ';

comment on column yamato_sv_level.depature_base_store is '発ベース店';

comment on column yamato_sv_level.sorting_cd is '仕分コード';

comment on column yamato_sv_level.service_level is 'サービスレベル';

comment on column yamato_sv_level.fastest_delivery_time_kbn is '最短お届け時間帯';

comment on column yamato_sv_level.start_date is '適用開始日';

comment on column yamato_sv_level.orm_rowid is 'データ行ID';

comment on column yamato_sv_level.created_user is '作成ユーザ';

comment on column yamato_sv_level.created_datetime is '作成日時';

comment on column yamato_sv_level.updated_user is '更新ユーザ';

comment on column yamato_sv_level.updated_datetime is '更新日時';

create unique index yamato_sv_level_ix0
on yamato_sv_level
(orm_rowid)
tablespace ts_shop_i01;
