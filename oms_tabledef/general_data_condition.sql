create table general_data_condition
(
    condition_pattern_id           numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    user_code                      numeric(38,0) not null,
    user_name                      varchar(20) not null,
    export_type                    varchar(256) not null,
    condition_pattern_name         varchar(50) not null,
    condition_pattern              text not null,
    access_type                    numeric(1,0) not null,
    remarks                        varchar(200),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint general_data_condition_pk primary key (condition_pattern_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table general_data_condition is '汎用データ出力条件';

comment on column general_data_condition.condition_pattern_id is '条件パターンID';

comment on column general_data_condition.shop_code is 'ショップコード';

comment on column general_data_condition.user_code is 'ユーザコード';

comment on column general_data_condition.user_name is '管理ユーザ名称';

comment on column general_data_condition.export_type is 'データ種別';

comment on column general_data_condition.condition_pattern_name is '条件パターン名称';

comment on column general_data_condition.condition_pattern is '条件パターン';

comment on column general_data_condition.access_type is 'アクセスタイプ';

comment on column general_data_condition.remarks is '備考';

comment on column general_data_condition.orm_rowid is 'データ行ID';

comment on column general_data_condition.created_user is '作成ユーザ';

comment on column general_data_condition.created_datetime is '作成日時';

comment on column general_data_condition.updated_user is '更新ユーザ';

comment on column general_data_condition.updated_datetime is '更新日時';

create unique index general_data_condition_ix0
on general_data_condition
(orm_rowid)
tablespace ts_shop_i01;
