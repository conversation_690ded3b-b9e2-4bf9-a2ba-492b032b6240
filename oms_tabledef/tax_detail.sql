create table tax_detail
(
    tax_group_code                 varchar(8) not null,
    tax_no                         numeric(3,0) not null,
    applied_start_date             timestamp(0) not null,
    applied_end_date               timestamp(0) not null,
    tax_rate                       numeric(3,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint tax_detail_pk primary key (tax_group_code, tax_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table tax_detail is '消費税明細';

comment on column tax_detail.tax_group_code is '消費税グループコード';

comment on column tax_detail.tax_no is '消費税番号';

comment on column tax_detail.applied_start_date is '消費税適用開始日';

comment on column tax_detail.applied_end_date is '消費税適用終了日';

comment on column tax_detail.tax_rate is '消費税率';

comment on column tax_detail.orm_rowid is 'データ行ID';

comment on column tax_detail.created_user is '作成ユーザ';

comment on column tax_detail.created_datetime is '作成日時';

comment on column tax_detail.updated_user is '更新ユーザ';

comment on column tax_detail.updated_datetime is '更新日時';

create unique index tax_detail_ix0
on tax_detail
(orm_rowid)
tablespace ts_shop_i01;

create index tax_detail_ix1
on tax_detail
(applied_start_date)
tablespace ts_shop_i01;

create index tax_detail_ix2
on tax_detail
(applied_end_date)
tablespace ts_shop_i01;
