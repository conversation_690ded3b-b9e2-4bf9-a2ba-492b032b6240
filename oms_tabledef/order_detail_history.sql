create table order_detail_history
(
    order_history_id               numeric(38,0) not null,
    order_no                       varchar(16) not null,
    order_detail_no                numeric(16,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    commodity_name                 varchar(100) not null,
    commodity_kind                 varchar(2) not null,
    baitai_code                    varchar(10) not null,
    baitai_name                    varchar(50) not null,
    hinban_code                    varchar(24) not null,
    standard_detail1_name          varchar(20),
    standard_detail2_name          varchar(20),
    purchasing_amount              numeric(8,0) not null,
    unit_price                     numeric(8,0) not null,
    retail_price                   numeric(8,0) not null,
    retail_tax                     numeric(10,2) not null,
    commodity_tax_group_code       varchar(8) not null,
    commodity_tax_no               numeric(3,0) not null,
    commodity_tax_rate             numeric(3,0) not null,
    commodity_tax                  numeric(10,2) not null,
    commodity_tax_type             numeric(1,0) not null,
    campaign_code                  varchar(16),
    campaign_name                  varchar(50),
    campaign_instructions_code     varchar(16),
    campaign_instructions_name     varchar(50),
    campaign_discount_rate         numeric(3,0),
    campaign_discount_price        numeric(8,0),
    present_campaign_instructions_code varchar(16),
    present_order_detail_no        numeric(16,0),
    age_limit_code                 numeric(8,0),
    age_limit_name                 varchar(50),
    age                            numeric(3,0),
    age_limit_confirm_type         numeric(1,0),
    applied_point_rate             numeric(8,0) not null,
    benefits_code                  varchar(10),
    benefits_name                  varchar(50),
    benefits_commodity_code        varchar(16),
    stock_management_type          numeric(1,0),
    stock_allocated_kbn            varchar(2) not null,
    allocated_warehouse_code       varchar(6),
    allocated_quantity             numeric(8,0) not null,
    arrival_reserved_quantity      numeric(8,0) not null,
    cancel_quantity                numeric(5,0) not null,
    henpin_qt                      numeric(5,0) not null,
    coupon_management_code         varchar(16),
    coupon_code                    varchar(16),
    coupon_name                    varchar(50),
    coupon_discount_rate           numeric(3,0),
    coupon_discount_price          numeric(8,0),
    ec_promotion_id                varchar(256),
    ec_promotion_name              varchar(256),
    ec_promotion_discount_price    numeric(8,0),
    ec_campaign_id                 varchar(256),
    ec_campaign_name               varchar(256),
    adjustment_price               numeric(8,0),
    keihi_hurikae_target_flg       numeric(1,0) not null,
    member_price_applied_flg       numeric(1,0) not null,
    shipping_charge_target_flg     numeric(1,0) not null,
    regular_contract_no            varchar(14),
    regular_contract_detail_no     numeric(3,0),
    regular_kaiji                  numeric(5,0) not null,
    regular_check_memo             varchar(1000),
    total_commodity_buy_count      numeric(5,0) not null,
    total_commodity_regular_kaiji  numeric(5,0) not null,
    regular_total_commodity_regular_kaiji numeric(5,0) not null,
    commodity_category_code        varchar(16),
    total_category_buy_count       numeric(5,0) not null,
    total_categoryregular_kaiji    numeric(5,0) not null,
    regular_total_categoryregular_kaiji numeric(5,0) not null,
    commodity_subcategory_code     varchar(16),
    total_subcategory_buy_count    numeric(5,0) not null,
    total_subcategoryregular_kaiji numeric(5,0) not null,
    regular_total_subcategoryregular_kaiji numeric(5,0) not null,
    commodity_subsubcategory_code  varchar(16),
    total_subsubcategory_buy_count numeric(5,0) not null,
    total_subsubcategoryregular_kaiji numeric(5,0) not null,
    regular_total_subsubcategoryregular_kaiji numeric(5,0) not null,
    grant_plan_point_prod_detail   numeric(10,0),
    reduction_plan_point_prod_detail numeric(10,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_detail_history_pk primary key (order_no, order_detail_no, order_history_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_detail_history is '受注明細履歴';

comment on column order_detail_history.order_history_id is '受注履歴ID';

comment on column order_detail_history.order_no is '受注番号';

comment on column order_detail_history.order_detail_no is '受注明細番号';

comment on column order_detail_history.shop_code is 'ショップコード';

comment on column order_detail_history.sku_code is 'SKUコード';

comment on column order_detail_history.commodity_code is '商品コード';

comment on column order_detail_history.commodity_name is '商品名称';

comment on column order_detail_history.commodity_kind is '商品種別';

comment on column order_detail_history.baitai_code is '媒体コード';

comment on column order_detail_history.baitai_name is '媒体名称';

comment on column order_detail_history.hinban_code is '品番コード';

comment on column order_detail_history.standard_detail1_name is '規格詳細1名称';

comment on column order_detail_history.standard_detail2_name is '規格詳細2名称';

comment on column order_detail_history.purchasing_amount is '購入商品数';

comment on column order_detail_history.unit_price is '商品単価';

comment on column order_detail_history.retail_price is '販売価格';

comment on column order_detail_history.retail_tax is '販売時消費税額';

comment on column order_detail_history.commodity_tax_group_code is '商品消費税グループコード';

comment on column order_detail_history.commodity_tax_no is '商品消費税番号';

comment on column order_detail_history.commodity_tax_rate is '商品消費税率';

comment on column order_detail_history.commodity_tax is '商品消費税額';

comment on column order_detail_history.commodity_tax_type is '商品消費税区分';

comment on column order_detail_history.campaign_code is 'キャンペーンコード';

comment on column order_detail_history.campaign_name is 'キャンペーン名称';

comment on column order_detail_history.campaign_instructions_code is 'キャンペーン設定コード';

comment on column order_detail_history.campaign_instructions_name is 'キャンペーン設定名称';

comment on column order_detail_history.campaign_discount_rate is 'キャンペーン値引率';

comment on column order_detail_history.campaign_discount_price is 'キャンペーン値引額';

comment on column order_detail_history.present_campaign_instructions_code is 'プレゼント付与元キャンペーン設定コード';

comment on column order_detail_history.present_order_detail_no is 'プレゼント付与元受注明細番号';

comment on column order_detail_history.age_limit_code is '年齢制限コード';

comment on column order_detail_history.age_limit_name is '年齢制限名称';

comment on column order_detail_history.age is '年齢';

comment on column order_detail_history.age_limit_confirm_type is '年齢制限自動確認区分';

comment on column order_detail_history.applied_point_rate is '適用ポイント付与率';

comment on column order_detail_history.benefits_code is '特典コード';

comment on column order_detail_history.benefits_name is '特典名称';

comment on column order_detail_history.benefits_commodity_code is '特典対象商品コード';

comment on column order_detail_history.stock_management_type is '在庫管理区分';

comment on column order_detail_history.stock_allocated_kbn is '在庫引当区分';

comment on column order_detail_history.allocated_warehouse_code is '引当倉庫コード';

comment on column order_detail_history.allocated_quantity is '引当数量';

comment on column order_detail_history.arrival_reserved_quantity is '入荷予定予約数量';

comment on column order_detail_history.cancel_quantity is 'キャンセル数量';

comment on column order_detail_history.henpin_qt is '返品数量';

comment on column order_detail_history.coupon_management_code is 'クーポン管理コード';

comment on column order_detail_history.coupon_code is 'クーポンコード';

comment on column order_detail_history.coupon_name is 'クーポン名称';

comment on column order_detail_history.coupon_discount_rate is 'クーポン値引率';

comment on column order_detail_history.coupon_discount_price is 'クーポン値引額';

comment on column order_detail_history.ec_promotion_id is 'ECプロモーションID';

comment on column order_detail_history.ec_promotion_name is 'ECプロモーション名';

comment on column order_detail_history.ec_promotion_discount_price is 'ECプロモーション値引額';

comment on column order_detail_history.ec_campaign_id is 'ECキャンペーンID';

comment on column order_detail_history.ec_campaign_name is 'ECキャンペーン名';

comment on column order_detail_history.adjustment_price is '調整額';

comment on column order_detail_history.keihi_hurikae_target_flg is '経費振替対象フラグ';

comment on column order_detail_history.member_price_applied_flg is '会員価格適用フラグ';

comment on column order_detail_history.shipping_charge_target_flg is '送料計算対象フラグ';

comment on column order_detail_history.regular_contract_no is '定期契約番号';

comment on column order_detail_history.regular_contract_detail_no is '定期契約明細番号';

comment on column order_detail_history.regular_kaiji is '定期回次';

comment on column order_detail_history.regular_check_memo is '定期チェックメモ';

comment on column order_detail_history.total_commodity_buy_count is '累計商品購入回数';

comment on column order_detail_history.total_commodity_regular_kaiji is '累計商品定期回次';

comment on column order_detail_history.regular_total_commodity_regular_kaiji is '累計商品定期回次（同一定期内）';

comment on column order_detail_history.commodity_category_code is '商品大分類';

comment on column order_detail_history.total_category_buy_count is '累計大分類購入回数';

comment on column order_detail_history.total_categoryregular_kaiji is '累計大分類定期回次';

comment on column order_detail_history.regular_total_categoryregular_kaiji is '累計大分類定期回次（同一定期内）';

comment on column order_detail_history.commodity_subcategory_code is '商品中分類';

comment on column order_detail_history.total_subcategory_buy_count is '累計中分類購入回数';

comment on column order_detail_history.total_subcategoryregular_kaiji is '累計中分類定期回次';

comment on column order_detail_history.regular_total_subcategoryregular_kaiji is '累計中分類定期回次（同一定期内）';

comment on column order_detail_history.commodity_subsubcategory_code is '商品小分類';

comment on column order_detail_history.total_subsubcategory_buy_count is '累計小分類購入回数';

comment on column order_detail_history.total_subsubcategoryregular_kaiji is '累計小分類定期回次';

comment on column order_detail_history.regular_total_subsubcategoryregular_kaiji is '累計小分類定期回次（同一定期内）';

comment on column order_detail_history.grant_plan_point_prod_detail is '付与予定ポイント数';

comment on column order_detail_history.reduction_plan_point_prod_detail is '利用予定ポイント数';

comment on column order_detail_history.orm_rowid is 'データ行ID';

comment on column order_detail_history.created_user is '作成ユーザ';

comment on column order_detail_history.created_datetime is '作成日時';

comment on column order_detail_history.updated_user is '更新ユーザ';

comment on column order_detail_history.updated_datetime is '更新日時';

create unique index order_detail_history_ix0
on order_detail_history
(order_history_id, orm_rowid)
tablespace ts_order_i01;
