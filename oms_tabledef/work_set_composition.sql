create table work_set_composition
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    child_commodity_code           varchar(16) not null,
    composition_quantity           numeric(2,0) not null,
    composition_order              numeric(2,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
)
tablespace ts_commodity_t01;

comment on table work_set_composition is 'セット構成品用ワークテーブル';

comment on column work_set_composition.shop_code is 'ショップコード';

comment on column work_set_composition.commodity_code is '商品コード';

comment on column work_set_composition.child_commodity_code is '子商品コード';

comment on column work_set_composition.composition_quantity is '構成数量';

comment on column work_set_composition.composition_order is '構成順序';

comment on column work_set_composition.orm_rowid is 'データ行ID';

comment on column work_set_composition.created_user is '作成ユーザ';

comment on column work_set_composition.created_datetime is '作成日時';

comment on column work_set_composition.updated_user is '更新ユーザ';

comment on column work_set_composition.updated_datetime is '更新日時';

