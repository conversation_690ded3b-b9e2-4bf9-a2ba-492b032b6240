create table installment_rule
(
    shop_code                      varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    payment_installment_type       varchar(2) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint installment_rule_pk primary key (shop_code, payment_method_no, payment_installment_type) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table installment_rule is '分割払いルール';

comment on column installment_rule.shop_code is 'ショップコード';

comment on column installment_rule.payment_method_no is '支払方法番号';

comment on column installment_rule.payment_installment_type is '支払分割区分';

comment on column installment_rule.orm_rowid is 'データ行ID';

comment on column installment_rule.created_user is '作成ユーザ';

comment on column installment_rule.created_datetime is '作成日時';

comment on column installment_rule.updated_user is '更新ユーザ';

comment on column installment_rule.updated_datetime is '更新日時';

create unique index installment_rule_ix0
on installment_rule
(orm_rowid)
tablespace ts_shop_i01;
