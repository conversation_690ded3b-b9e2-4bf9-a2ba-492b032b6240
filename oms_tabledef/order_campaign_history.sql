create table order_campaign_history
(
    order_history_id               numeric(38,0) not null,
    order_no                       varchar(16) not null,
    campaign_instructions_code     varchar(16) not null,
    campaign_instructions_name     varchar(50) not null,
    campaign_description           varchar(100),
    campaign_end_date              timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_campaign_history_pk primary key (order_history_id, order_no, campaign_instructions_code) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_campaign_history is '受注キャンペーン適用履歴';

comment on column order_campaign_history.order_history_id is '受注履歴ID';

comment on column order_campaign_history.order_no is '受注番号';

comment on column order_campaign_history.campaign_instructions_code is 'キャンペーン設定コード';

comment on column order_campaign_history.campaign_instructions_name is 'キャンペーン設定名称';

comment on column order_campaign_history.campaign_description is 'キャンペーン内容';

comment on column order_campaign_history.campaign_end_date is 'キャンペーン適用終了日';

comment on column order_campaign_history.orm_rowid is 'データ行ID';

comment on column order_campaign_history.created_user is '作成ユーザ';

comment on column order_campaign_history.created_datetime is '作成日時';

comment on column order_campaign_history.updated_user is '更新ユーザ';

comment on column order_campaign_history.updated_datetime is '更新日時';

