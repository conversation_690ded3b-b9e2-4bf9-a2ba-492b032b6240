create table tenpo
(
    tenpo_code                     varchar(3) not null,
    tenpo_name                     varchar(30),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint tenpo_pk primary key (tenpo_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table tenpo is '店舗';

comment on column tenpo.tenpo_code is '店舗コード';

comment on column tenpo.tenpo_name is '店舗名称';

comment on column tenpo.orm_rowid is 'データ行ID';

comment on column tenpo.created_user is '作成ユーザ';

comment on column tenpo.created_datetime is '作成日時';

comment on column tenpo.updated_user is '更新ユーザ';

comment on column tenpo.updated_datetime is '更新日時';

create unique index tenpo_ix0
on tenpo
(orm_rowid)
tablespace ts_shop_i01;
