create table confirm_code_issue
(
    email                          varchar(256) not null,
    confirm_code                   varchar(6) not null,
    issued_datetime                timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint confirm_code_issue_pk primary key (email) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table confirm_code_issue is '確認コード発行';

comment on column confirm_code_issue.email is 'メールアドレス';

comment on column confirm_code_issue.confirm_code is '確認コード';

comment on column confirm_code_issue.issued_datetime is '確認コード発行日時';

comment on column confirm_code_issue.orm_rowid is 'データ行ID';

comment on column confirm_code_issue.created_user is '作成ユーザ';

comment on column confirm_code_issue.created_datetime is '作成日時';

comment on column confirm_code_issue.updated_user is '更新ユーザ';

comment on column confirm_code_issue.updated_datetime is '更新日時';

create unique index confirm_code_issue_ix0
on confirm_code_issue
(orm_rowid)
tablespace ts_customer_i01;
