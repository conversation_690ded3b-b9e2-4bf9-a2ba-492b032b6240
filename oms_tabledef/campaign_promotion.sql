create table campaign_promotion
(
    campaign_instructions_code     varchar(16) not null,
    promotion_no                   numeric(9,0) not null,
    promotion_type                 varchar(2) not null,
    shop_code                      varchar(16),
    commodity_code                 varchar(16),
    commodity_name                 varchar(100),
    present_qt                     numeric(3,0),
    discount_rate                  numeric(3,0),
    discount_amount                numeric(8,0),
    discount_retail_price          numeric(8,0),
    shipping_charge                numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_promotion_pk primary key (campaign_instructions_code, promotion_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_promotion is 'キャンペーン設定プロモーション';

comment on column campaign_promotion.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_promotion.promotion_no is 'プロモーション番号';

comment on column campaign_promotion.promotion_type is 'プロモーション種別';

comment on column campaign_promotion.shop_code is 'ショップコード';

comment on column campaign_promotion.commodity_code is '商品コード';

comment on column campaign_promotion.commodity_name is '商品名称';

comment on column campaign_promotion.present_qt is 'プレゼント数量';

comment on column campaign_promotion.discount_rate is '値引率';

comment on column campaign_promotion.discount_amount is '値引額';

comment on column campaign_promotion.discount_retail_price is '値引後販売価格';

comment on column campaign_promotion.shipping_charge is '送料';

comment on column campaign_promotion.orm_rowid is 'データ行ID';

comment on column campaign_promotion.created_user is '作成ユーザ';

comment on column campaign_promotion.created_datetime is '作成日時';

comment on column campaign_promotion.updated_user is '更新ユーザ';

comment on column campaign_promotion.updated_datetime is '更新日時';

create unique index campaign_promotion_ix0
on campaign_promotion
(orm_rowid)
tablespace ts_commodity_i01;
