create table stock_io_detail
(
    stock_io_id                    numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    stock_io_date                  timestamp(0) not null,
    sku_code                       varchar(24) not null,
    stock_io_quantity              numeric(8,0) not null,
    stock_io_type                  numeric(1,0) not null,
    memo                           varchar(200),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint stock_io_detail_pk primary key (stock_io_id) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table stock_io_detail is '入出庫明細';

comment on column stock_io_detail.stock_io_id is '入出庫行ID';

comment on column stock_io_detail.shop_code is 'ショップコード';

comment on column stock_io_detail.stock_io_date is '入出庫日';

comment on column stock_io_detail.sku_code is 'SKUコード';

comment on column stock_io_detail.stock_io_quantity is '入出庫数量';

comment on column stock_io_detail.stock_io_type is '入出庫区分';

comment on column stock_io_detail.memo is 'メモ';

comment on column stock_io_detail.orm_rowid is 'データ行ID';

comment on column stock_io_detail.created_user is '作成ユーザ';

comment on column stock_io_detail.created_datetime is '作成日時';

comment on column stock_io_detail.updated_user is '更新ユーザ';

comment on column stock_io_detail.updated_datetime is '更新日時';

create unique index stock_io_detail_ix0
on stock_io_detail
(orm_rowid)
tablespace ts_commodity_i01;

create index stock_io_detail_ix1
on stock_io_detail
(shop_code, sku_code)
tablespace ts_commodity_i01;

create index stock_io_detail_ix2
on stock_io_detail
(stock_io_date)
tablespace ts_commodity_i01;
