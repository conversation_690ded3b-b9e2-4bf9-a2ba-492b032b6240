create table inc_work_detail
(
    shop_code                      varchar(16) not null,
    inc_work_code                  varchar(16) not null,
    inc_work_detail_no             numeric(8,0) not null,
    inc_work_choices               varchar(30) not null,
    inc_work_price                 numeric(8,0) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint inc_work_detail_pk primary key (shop_code, inc_work_code, inc_work_detail_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table inc_work_detail is '付帯サービス詳細';

comment on column inc_work_detail.shop_code is 'ショップコード';

comment on column inc_work_detail.inc_work_code is '付帯サービスコード';

comment on column inc_work_detail.inc_work_detail_no is '付帯サービス詳細番号';

comment on column inc_work_detail.inc_work_choices is '付帯サービス選択肢名称';

comment on column inc_work_detail.inc_work_price is '付帯サービス価格';

comment on column inc_work_detail.display_order is '表示順';

comment on column inc_work_detail.orm_rowid is 'データ行ID';

comment on column inc_work_detail.created_user is '作成ユーザ';

comment on column inc_work_detail.created_datetime is '作成日時';

comment on column inc_work_detail.updated_user is '更新ユーザ';

comment on column inc_work_detail.updated_datetime is '更新日時';

create unique index inc_work_detail_ix0
on inc_work_detail
(orm_rowid)
tablespace ts_commodity_i01;
