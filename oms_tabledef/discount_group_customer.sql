create table discount_group_customer
(
    customer_group_code            varchar(16) not null,
    customer_code                  varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint discount_group_customer_pk primary key (customer_group_code, customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table discount_group_customer is '値引グループ関連付け顧客';

comment on column discount_group_customer.customer_group_code is '顧客グループコード';

comment on column discount_group_customer.customer_code is '顧客コード';

comment on column discount_group_customer.orm_rowid is 'データ行ID';

comment on column discount_group_customer.created_user is '作成ユーザ';

comment on column discount_group_customer.created_datetime is '作成日時';

comment on column discount_group_customer.updated_user is '更新ユーザ';

comment on column discount_group_customer.updated_datetime is '更新日時';

create unique index discount_group_customer_ix0
on discount_group_customer
(orm_rowid)
tablespace ts_customer_i01;
