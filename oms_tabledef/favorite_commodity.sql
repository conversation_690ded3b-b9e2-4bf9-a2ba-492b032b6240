create table favorite_commodity
(
    customer_code                  varchar(16) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    favorite_register_date         timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint favorite_commodity_pk primary key (customer_code, shop_code, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table favorite_commodity is 'お気に入り商品';

comment on column favorite_commodity.customer_code is '顧客コード';

comment on column favorite_commodity.shop_code is 'ショップコード';

comment on column favorite_commodity.sku_code is 'SKUコード';

comment on column favorite_commodity.favorite_register_date is 'お気に入り登録日';

comment on column favorite_commodity.orm_rowid is 'データ行ID';

comment on column favorite_commodity.created_user is '作成ユーザ';

comment on column favorite_commodity.created_datetime is '作成日時';

comment on column favorite_commodity.updated_user is '更新ユーザ';

comment on column favorite_commodity.updated_datetime is '更新日時';

create unique index favorite_commodity_ix0
on favorite_commodity
(orm_rowid)
tablespace ts_commodity_i01;
