create table customer_attribute_choice
(
    customer_attribute_no          numeric(8,0) not null,
    customer_attribute_choices_no  numeric(8,0) not null,
    customer_attribute_choices     varchar(200) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_attribute_choice_pk primary key (customer_attribute_no, customer_attribute_choices_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_attribute_choice is '顧客属性選択肢名';

comment on column customer_attribute_choice.customer_attribute_no is '顧客属性番号';

comment on column customer_attribute_choice.customer_attribute_choices_no is '顧客属性選択肢番号';

comment on column customer_attribute_choice.customer_attribute_choices is '顧客属性選択肢名称';

comment on column customer_attribute_choice.display_order is '表示順';

comment on column customer_attribute_choice.orm_rowid is 'データ行ID';

comment on column customer_attribute_choice.created_user is '作成ユーザ';

comment on column customer_attribute_choice.created_datetime is '作成日時';

comment on column customer_attribute_choice.updated_user is '更新ユーザ';

comment on column customer_attribute_choice.updated_datetime is '更新日時';

create unique index customer_attribute_choice_ix0
on customer_attribute_choice
(orm_rowid)
tablespace ts_customer_i01;
