create table customer_attribute
(
    customer_attribute_no          numeric(8,0) not null,
    customer_attribute_name        varchar(40) not null,
    customer_attribute_type        numeric(1,0) not null,
    display_flg                    numeric(1,0) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_attribute_pk primary key (customer_attribute_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_attribute is '顧客属性';

comment on column customer_attribute.customer_attribute_no is '顧客属性番号';

comment on column customer_attribute.customer_attribute_name is '顧客属性名称';

comment on column customer_attribute.customer_attribute_type is '顧客属性区分';

comment on column customer_attribute.display_flg is '表示フラグ';

comment on column customer_attribute.display_order is '表示順';

comment on column customer_attribute.orm_rowid is 'データ行ID';

comment on column customer_attribute.created_user is '作成ユーザ';

comment on column customer_attribute.created_datetime is '作成日時';

comment on column customer_attribute.updated_user is '更新ユーザ';

comment on column customer_attribute.updated_datetime is '更新日時';

create unique index customer_attribute_ix0
on customer_attribute
(orm_rowid)
tablespace ts_customer_i01;

create index customer_attribute_ix1
on customer_attribute
(display_flg)
tablespace ts_customer_i01;
