create table gift_commodity
(
    shop_code                      varchar(16) not null,
    gift_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint gift_commodity_pk primary key (shop_code, gift_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table gift_commodity is 'ギフト対象商品';

comment on column gift_commodity.shop_code is 'ショップコード';

comment on column gift_commodity.gift_code is 'ギフトコード';

comment on column gift_commodity.commodity_code is '商品コード';

comment on column gift_commodity.orm_rowid is 'データ行ID';

comment on column gift_commodity.created_user is '作成ユーザ';

comment on column gift_commodity.created_datetime is '作成日時';

comment on column gift_commodity.updated_user is '更新ユーザ';

comment on column gift_commodity.updated_datetime is '更新日時';

create unique index gift_commodity_ix0
on gift_commodity
(orm_rowid)
tablespace ts_commodity_i01;
