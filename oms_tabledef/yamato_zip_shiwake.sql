create table yamato_zip_shiwake
(
    postal_code                    varchar(7) not null,
    mailbox_sorting_cd             varchar(7),
    takkyubin_sorting_cd           varchar(7),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint yamato_zip_shiwake_pk primary key (postal_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table yamato_zip_shiwake is 'ヤマト郵便番号対応仕分マスタ';

comment on column yamato_zip_shiwake.postal_code is '郵便番号';

comment on column yamato_zip_shiwake.mailbox_sorting_cd is '仕分コードメール便';

comment on column yamato_zip_shiwake.takkyubin_sorting_cd is '仕分コード宅急便';

comment on column yamato_zip_shiwake.orm_rowid is 'データ行ID';

comment on column yamato_zip_shiwake.created_user is '作成ユーザ';

comment on column yamato_zip_shiwake.created_datetime is '作成日時';

comment on column yamato_zip_shiwake.updated_user is '更新ユーザ';

comment on column yamato_zip_shiwake.updated_datetime is '更新日時';

create unique index yamato_zip_shiwake_ix0
on yamato_zip_shiwake
(orm_rowid)
tablespace ts_shop_i01;
