create table returns_address
(
    henpin_request_no              varchar(10) not null,
    shipping_no                    varchar(16) not null,
    order_no                       varchar(16) not null,
    shop_code                      varchar(16) not null,
    customer_code                  varchar(16),
    neo_customer_no                varchar(12),
    address_no                     numeric(8,0),
    shipping_charge                numeric(8,0) not null,
    shipping_charge_tax_type       numeric(1,0),
    shipping_charge_tax_group_code varchar(8),
    shipping_charge_tax_no         numeric(3,0),
    shipping_charge_tax_rate       numeric(3,0),
    shipping_charge_tax            numeric(10,2),
    before_shipping_charge         numeric(8,0),
    before_shipping_charge_tax     numeric(10,2),
    shipping_bill_flg              numeric(1,0) not null,
    pickup_datetime                timestamp(0),
    reship_memo                    varchar(20),
    yamato_bill_flg                numeric(1,0) not null,
    yamato_shipping_amount_flg     numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint returns_address_pk primary key (henpin_request_no, shipping_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table returns_address is '返品届け先';

comment on column returns_address.henpin_request_no is '返品依頼番号';

comment on column returns_address.shipping_no is '出荷番号';

comment on column returns_address.order_no is '受注番号';

comment on column returns_address.shop_code is 'ショップコード';

comment on column returns_address.customer_code is '顧客コード';

comment on column returns_address.neo_customer_no is '顧客番号';

comment on column returns_address.address_no is 'アドレス帳番号';

comment on column returns_address.shipping_charge is '送料';

comment on column returns_address.shipping_charge_tax_type is '送料消費税区分';

comment on column returns_address.shipping_charge_tax_group_code is '送料消費税グループコード';

comment on column returns_address.shipping_charge_tax_no is '送料消費税番号';

comment on column returns_address.shipping_charge_tax_rate is '送料消費税率';

comment on column returns_address.shipping_charge_tax is '送料消費税額';

comment on column returns_address.before_shipping_charge is '返品前送料';

comment on column returns_address.before_shipping_charge_tax is '返品前送料消費税額';

comment on column returns_address.shipping_bill_flg is '送料請求フラグ';

comment on column returns_address.pickup_datetime is '引取日時';

comment on column returns_address.reship_memo is '再発送配送メモ';

comment on column returns_address.yamato_bill_flg is 'ヤマト商品代請求フラグ';

comment on column returns_address.yamato_shipping_amount_flg is 'ヤマト送料請求フラグ';

comment on column returns_address.orm_rowid is 'データ行ID';

comment on column returns_address.created_user is '作成ユーザ';

comment on column returns_address.created_datetime is '作成日時';

comment on column returns_address.updated_user is '更新ユーザ';

comment on column returns_address.updated_datetime is '更新日時';

create unique index returns_address_ix0
on returns_address
(orm_rowid)
tablespace ts_order_i01;
