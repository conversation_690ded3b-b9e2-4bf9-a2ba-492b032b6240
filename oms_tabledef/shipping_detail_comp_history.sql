create table shipping_detail_comp_history
(
    order_history_id               numeric(38,0) not null,
    shipping_no                    varchar(16) not null,
    shipping_detail_no             numeric(16,0) not null,
    composition_no                 numeric(2,0) not null,
    shop_code                      varchar(16) not null,
    parent_commodity_code          varchar(16) not null,
    parent_sku_code                varchar(24) not null,
    child_commodity_code           varchar(16) not null,
    child_sku_code                 varchar(24) not null,
    commodity_name                 varchar(100) not null,
    standard_detail1_name          varchar(20),
    standard_detail2_name          varchar(20),
    unit_price                     numeric(8,0) not null,
    discount_amount                numeric(8,0),
    retail_price                   numeric(8,0) not null,
    retail_tax                     numeric(10,2) not null,
    commodity_tax_group_code       varchar(8) not null,
    commodity_tax_no               numeric(3,0) not null,
    commodity_tax_rate             numeric(3,0) not null,
    commodity_tax                  numeric(10,2) not null,
    commodity_tax_type             numeric(1,0) not null,
    composition_quantity           numeric(2,0) not null,
    stock_management_type          numeric(1,0) not null,
    stock_allocated_kbn            varchar(2) not null,
    allocated_warehouse_code       varchar(6),
    allocated_quantity             numeric(8,0) not null,
    arrival_reserved_quantity      numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_detail_comp_hist_pk primary key (order_history_id, shipping_no, shipping_detail_no, composition_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table shipping_detail_comp_history is '出荷明細構成品履歴';

comment on column shipping_detail_comp_history.order_history_id is '受注履歴ID';

comment on column shipping_detail_comp_history.shipping_no is '出荷番号';

comment on column shipping_detail_comp_history.shipping_detail_no is '出荷明細番号';

comment on column shipping_detail_comp_history.composition_no is '構成品項番';

comment on column shipping_detail_comp_history.shop_code is 'ショップコード';

comment on column shipping_detail_comp_history.parent_commodity_code is '親商品コード';

comment on column shipping_detail_comp_history.parent_sku_code is '親SKUコード';

comment on column shipping_detail_comp_history.child_commodity_code is '子商品コード';

comment on column shipping_detail_comp_history.child_sku_code is '子SKUコード';

comment on column shipping_detail_comp_history.commodity_name is '商品名称';

comment on column shipping_detail_comp_history.standard_detail1_name is '規格詳細1名称';

comment on column shipping_detail_comp_history.standard_detail2_name is '規格詳細2名称';

comment on column shipping_detail_comp_history.unit_price is '商品単価';

comment on column shipping_detail_comp_history.discount_amount is '値引額';

comment on column shipping_detail_comp_history.retail_price is '販売価格';

comment on column shipping_detail_comp_history.retail_tax is '販売時消費税額';

comment on column shipping_detail_comp_history.commodity_tax_group_code is '商品消費税グループコード';

comment on column shipping_detail_comp_history.commodity_tax_no is '商品消費税番号';

comment on column shipping_detail_comp_history.commodity_tax_rate is '商品消費税率';

comment on column shipping_detail_comp_history.commodity_tax is '商品消費税額';

comment on column shipping_detail_comp_history.commodity_tax_type is '商品消費税区分';

comment on column shipping_detail_comp_history.composition_quantity is '構成数量';

comment on column shipping_detail_comp_history.stock_management_type is '在庫管理区分';

comment on column shipping_detail_comp_history.stock_allocated_kbn is '在庫引当区分';

comment on column shipping_detail_comp_history.allocated_warehouse_code is '引当倉庫コード';

comment on column shipping_detail_comp_history.allocated_quantity is '引当数量';

comment on column shipping_detail_comp_history.arrival_reserved_quantity is '入荷予定予約数量';

comment on column shipping_detail_comp_history.orm_rowid is 'データ行ID';

comment on column shipping_detail_comp_history.created_user is '作成ユーザ';

comment on column shipping_detail_comp_history.created_datetime is '作成日時';

comment on column shipping_detail_comp_history.updated_user is '更新ユーザ';

comment on column shipping_detail_comp_history.updated_datetime is '更新日時';

create unique index shipping_detail_comp_hist_ix0
on shipping_detail_comp_history
(order_history_id, orm_rowid)
tablespace ts_order_i01;
