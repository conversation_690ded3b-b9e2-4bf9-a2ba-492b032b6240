create table gmo_payment_mng
(
    authority_no                   varchar(32) not null,
    order_no                       varchar(16) not null,
    card_password                  varchar(32),
    bill_price                     numeric(10,0) not null,
    ext_payment_method_type        varchar(2) not null,
    gmo_link_status                varchar(1) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint gmo_payment_mng_pk primary key (authority_no, order_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table gmo_payment_mng is 'GMO請求情報管理';

comment on column gmo_payment_mng.authority_no is 'オーソリ識別番号';

comment on column gmo_payment_mng.order_no is '受注番号';

comment on column gmo_payment_mng.card_password is 'カードパスワード';

comment on column gmo_payment_mng.bill_price is '請求額';

comment on column gmo_payment_mng.ext_payment_method_type is '支払方法区分（拡張）';

comment on column gmo_payment_mng.gmo_link_status is 'GMO連携ステータス';

comment on column gmo_payment_mng.orm_rowid is 'データ行ID';

comment on column gmo_payment_mng.created_user is '作成ユーザ';

comment on column gmo_payment_mng.created_datetime is '作成日時';

comment on column gmo_payment_mng.updated_user is '更新ユーザ';

comment on column gmo_payment_mng.updated_datetime is '更新日時';

create unique index gmo_payment_mng_ix0
on gmo_payment_mng
(orm_rowid)
tablespace ts_order_i01;
