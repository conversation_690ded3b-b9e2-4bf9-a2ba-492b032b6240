create table work_return_change_detail
(
    slip_ino                       varchar(14) not null,
    print_seq                      numeric(3,0) not null,
    goods_code                     varchar(10),
    goods_name                     varchar(100),
    qty                            numeric(7,0),
    amt                            numeric(10,0),
    return_text                    varchar(20),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_return_change_detail_pk primary key (slip_ino, print_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_return_change_detail is '返品交換票明細ワーク';

comment on column work_return_change_detail.slip_ino is '基幹出荷指示番号';

comment on column work_return_change_detail.print_seq is 'シーケンス';

comment on column work_return_change_detail.goods_code is '商品番号';

comment on column work_return_change_detail.goods_name is '商品名';

comment on column work_return_change_detail.qty is '数量';

comment on column work_return_change_detail.amt is '金額';

comment on column work_return_change_detail.return_text is '返品情報';

comment on column work_return_change_detail.orm_rowid is 'データ行ID';

comment on column work_return_change_detail.created_user is '作成ユーザ';

comment on column work_return_change_detail.created_datetime is '作成日時';

comment on column work_return_change_detail.updated_user is '更新ユーザ';

comment on column work_return_change_detail.updated_datetime is '更新日時';

create unique index work_return_change_detail_ix0
on work_return_change_detail
(orm_rowid)
tablespace ts_order_i01;
