create table work_commodity_keyword
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    search_keyword                 varchar(150) not null,
    constraint work_commodity_keyword_pk primary key (shop_code, commodity_code, search_keyword) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table work_commodity_keyword is '商品キーワード用ワークテーブル';

comment on column work_commodity_keyword.shop_code is 'ショップコード';

comment on column work_commodity_keyword.commodity_code is '商品コード';

comment on column work_commodity_keyword.search_keyword is '検索キーワード';

create unique index work_commodity_keyword_ix1
on work_commodity_keyword
(search_keyword, shop_code, commodity_code)
tablespace ts_commodity_i01;
