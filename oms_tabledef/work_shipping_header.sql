create table work_shipping_header
(
    accept_no                      varchar(22) not null,
    cust_no                        varchar(12),
    record_no                      varchar(6),
    cust_name                      varchar(100),
    post_no                        varchar(8),
    addr1                          varchar(50),
    addr2                          varchar(50),
    addr3                          varchar(50),
    tel_no                         varchar(15),
    prefecture_code                numeric(2,0),
    cust_flg                       numeric(1,0),
    order_date                     varchar(8),
    pay_kb                         varchar(2),
    total_price                    numeric(10,0),
    delive_cust_name               varchar(100),
    delive_post_no                 varchar(8),
    delive_addr1                   varchar(50),
    delive_addr2                   varchar(50),
    delive_addr3                   varchar(50),
    delive_tel_no                  varchar(15),
    gift_flg                       varchar(1),
    kibou_ymd                      varchar(8),
    night_flg                      numeric(2,0),
    cosme_price                    numeric(10,0),
    health_price                   numeric(10,0),
    inner_price                    numeric(10,0),
    update_date                    varchar(8),
    chit_print_date                varchar(8),
    yamato_bar_code                varchar(14),
    gyosha_flg                     varchar(1),
    status_flg                     varchar(1),
    slip_ono                       varchar(20),
    gift_flg2                      varchar(1),
    client_name                    varchar(100),
    client_delive_post_no          varchar(8),
    client_delive_addr1            varchar(50),
    client_delive_addr2            varchar(100),
    client_delive_addr3            varchar(1),
    client_tel_no                  varchar(15),
    order_no                       varchar(14),
    delivery_flag                  varchar(8),
    clinic_name                    varchar(100),
    shipment_date                  timestamp(0),
    shipment_plan_date             timestamp(0),
    pack_cnt                       numeric(1,0),
    store_code                     varchar(8),
    period_flg                     varchar(2),
    delivery_box_gb                varchar(2),
    air_delivery_yn                varchar(1),
    tax_amt                        numeric(10,0),
    conveni_yn                     varchar(1),
    pudo_yn                        varchar(1),
    inplan_yn                      varchar(1),
    over_stock_yn                  varchar(1),
    reserve_order_yn               varchar(1),
    gift_rapping_yn                varchar(1),
    kanshi_yn                      varchar(1),
    fusoku_yn                      varchar(1),
    airplane_yn                    varchar(1),
    satofuru_yn                    varchar(1),
    tokusha_yn                     varchar(1),
    rakugaki_yn                    varchar(1),
    multi_sample_yn                varchar(1),
    slip_size_code                 varchar(1),
    wh_code                        varchar(3),
    agent_cd                       varchar(13),
    import_yn                      varchar(1),
    import_date                    timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_shipping_header_pk primary key (accept_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_shipping_header is '出荷ヘッダワーク';

comment on column work_shipping_header.accept_no is '基幹出荷指示番号';

comment on column work_shipping_header.cust_no is '顧客番号';

comment on column work_shipping_header.record_no is 'レコード番号';

comment on column work_shipping_header.cust_name is '顧客名';

comment on column work_shipping_header.post_no is 'ギフト注文主の郵便番号';

comment on column work_shipping_header.addr1 is 'ギフト注文主の住所１';

comment on column work_shipping_header.addr2 is 'ギフト注文主の住所２';

comment on column work_shipping_header.addr3 is 'ギフト注文主の住所３';

comment on column work_shipping_header.tel_no is '電話番号';

comment on column work_shipping_header.prefecture_code is '都道府県コード';

comment on column work_shipping_header.cust_flg is '新規会員フラグ';

comment on column work_shipping_header.order_date is '注文日';

comment on column work_shipping_header.pay_kb is '支払い方法';

comment on column work_shipping_header.total_price is '決済額';

comment on column work_shipping_header.delive_cust_name is '配送先の顧客名';

comment on column work_shipping_header.delive_post_no is '配送先の郵便番号';

comment on column work_shipping_header.delive_addr1 is '配送先の住所１';

comment on column work_shipping_header.delive_addr2 is '配送先の住所２';

comment on column work_shipping_header.delive_addr3 is '配送先の住所３';

comment on column work_shipping_header.delive_tel_no is '配送先の電話番号';

comment on column work_shipping_header.gift_flg is 'ギフト注文フラグ';

comment on column work_shipping_header.kibou_ymd is '配送指定日';

comment on column work_shipping_header.night_flg is '時間帯指定コード';

comment on column work_shipping_header.cosme_price is '未使用_cosme_price';

comment on column work_shipping_header.health_price is '未使用_health_price';

comment on column work_shipping_header.inner_price is '未使用_inner_price';

comment on column work_shipping_header.update_date is 'WMS更新日';

comment on column work_shipping_header.chit_print_date is '検品日';

comment on column work_shipping_header.yamato_bar_code is '伝票番号';

comment on column work_shipping_header.gyosha_flg is '配送業者コード';

comment on column work_shipping_header.status_flg is 'データ識別区分';

comment on column work_shipping_header.slip_ono is '受取店舗コード';

comment on column work_shipping_header.gift_flg2 is 'ギフト注文フラグＢ';

comment on column work_shipping_header.client_name is 'ギフト注文主の顧客名';

comment on column work_shipping_header.client_delive_post_no is 'ギフト注文主の郵便番号Ｂ';

comment on column work_shipping_header.client_delive_addr1 is 'ギフト注文主の住所１Ｂ';

comment on column work_shipping_header.client_delive_addr2 is 'ギフト注文主の住所２Ｂ';

comment on column work_shipping_header.client_delive_addr3 is 'ギフト注文主の住所３Ｂ';

comment on column work_shipping_header.client_tel_no is 'ギフト注文主の電話番号Ｂ';

comment on column work_shipping_header.order_no is '注文番号';

comment on column work_shipping_header.delivery_flag is '未使用_delivery_flag';

comment on column work_shipping_header.clinic_name is '未使用_clinic_name';

comment on column work_shipping_header.shipment_date is 'ゲート通過日';

comment on column work_shipping_header.shipment_plan_date is '出荷予定日';

comment on column work_shipping_header.pack_cnt is '箱数';

comment on column work_shipping_header.store_code is '着店コード';

comment on column work_shipping_header.period_flg is '定期便フラグ';

comment on column work_shipping_header.delivery_box_gb is '宅配ボックス区分';

comment on column work_shipping_header.air_delivery_yn is '航空便フラグ';

comment on column work_shipping_header.tax_amt is '消費税額';

comment on column work_shipping_header.conveni_yn is 'ヤマト自宅外受取フラグ';

comment on column work_shipping_header.pudo_yn is 'ヤマトPUDO受取フラグ';

comment on column work_shipping_header.inplan_yn is '入庫受注予定照会';

comment on column work_shipping_header.over_stock_yn is '過受注';

comment on column work_shipping_header.reserve_order_yn is '別送対応';

comment on column work_shipping_header.gift_rapping_yn is 'ラッピング';

comment on column work_shipping_header.kanshi_yn is '注文監視';

comment on column work_shipping_header.fusoku_yn is '不足申告';

comment on column work_shipping_header.airplane_yn is '航空便不可';

comment on column work_shipping_header.satofuru_yn is 'さとふる集荷';

comment on column work_shipping_header.tokusha_yn is '特別社販';

comment on column work_shipping_header.rakugaki_yn is 'LINEプレゼント発送';

comment on column work_shipping_header.multi_sample_yn is '予約受注';

comment on column work_shipping_header.slip_size_code is '納品書区分';

comment on column work_shipping_header.wh_code is '倉庫コード';

comment on column work_shipping_header.agent_cd is '取扱店ＣＤ';

comment on column work_shipping_header.import_yn is 'ロジマネ取込済フラグ';

comment on column work_shipping_header.import_date is 'ロジマネ取込日';

comment on column work_shipping_header.orm_rowid is 'データ行ID';

comment on column work_shipping_header.created_user is '作成ユーザ';

comment on column work_shipping_header.created_datetime is '作成日時';

comment on column work_shipping_header.updated_user is '更新ユーザ';

comment on column work_shipping_header.updated_datetime is '更新日時';

create unique index work_shipping_header_ix0
on work_shipping_header
(orm_rowid)
tablespace ts_order_i01;
