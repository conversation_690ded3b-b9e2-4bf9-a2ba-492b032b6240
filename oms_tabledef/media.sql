create table media
(
    baitai_code                    varchar(10) not null,
    baitai_name                    varchar(50) not null,
    promotion_code                 varchar(8),
    baitai_kbn                     varchar(2) not null,
    media_kbn1                     varchar(5) not null,
    media_kbn2                     varchar(15) not null,
    genko_kind                     varchar(2) not null,
    application_start_datetime     timestamp(0) not null,
    application_end_datetime       timestamp(0) not null,
    publish_days                   numeric(5,0),
    charge_user_code               numeric(38,0),
    haifu_unit_price               numeric(9,0),
    print_unit_price               numeric(9,0),
    senden_unit_price              numeric(9,0),
    benefits_code                  varchar(10),
    coupon_management_code         varchar(16),
    baitai_memo                    varchar(100),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint media_pk primary key (baitai_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table media is '媒体マスタ';

comment on column media.baitai_code is '媒体コード';

comment on column media.baitai_name is '媒体名称';

comment on column media.promotion_code is 'プロモーションコード';

comment on column media.baitai_kbn is '媒体区分';

comment on column media.media_kbn1 is 'メディア区分１';

comment on column media.media_kbn2 is 'メディア区分２';

comment on column media.genko_kind is '原稿種別';

comment on column media.application_start_datetime is '申込受付開始日時';

comment on column media.application_end_datetime is '申込受付終了日時';

comment on column media.publish_days is '掲載日数';

comment on column media.charge_user_code is '担当ユーザコード';

comment on column media.haifu_unit_price is '配布単価';

comment on column media.print_unit_price is '印刷単価';

comment on column media.senden_unit_price is '宣伝費';

comment on column media.benefits_code is '特典コード';

comment on column media.coupon_management_code is 'クーポン管理コード';

comment on column media.baitai_memo is '媒体メモ';

comment on column media.orm_rowid is 'データ行ID';

comment on column media.created_user is '作成ユーザ';

comment on column media.created_datetime is '作成日時';

comment on column media.updated_user is '更新ユーザ';

comment on column media.updated_datetime is '更新日時';

create unique index media_ix0
on media
(orm_rowid)
tablespace ts_shop_i01;
