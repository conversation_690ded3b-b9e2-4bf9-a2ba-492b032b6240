create table customer_privilege_history
(
    customer_code                  varchar(16) not null,
    benefits_code                  varchar(10) not null,
    neo_customer_no                varchar(12) not null,
    order_no                       varchar(16),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_privilege_history_pk primary key (customer_code, benefits_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_privilege_history is '顧客特典付与履歴';

comment on column customer_privilege_history.customer_code is '顧客コード';

comment on column customer_privilege_history.benefits_code is '特典コード';

comment on column customer_privilege_history.neo_customer_no is '顧客番号';

comment on column customer_privilege_history.order_no is '受注番号';

comment on column customer_privilege_history.orm_rowid is 'データ行ID';

comment on column customer_privilege_history.created_user is '作成ユーザ';

comment on column customer_privilege_history.created_datetime is '作成日時';

comment on column customer_privilege_history.updated_user is '更新ユーザ';

comment on column customer_privilege_history.updated_datetime is '更新日時';

create unique index customer_privilege_history_ix0
on customer_privilege_history
(orm_rowid)
tablespace ts_customer_i01;

create index customer_privilege_history_ix1
on customer_privilege_history
(neo_customer_no)
tablespace ts_customer_i01;
