create table regular_sale_payment
(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_payment_pk primary key (shop_code, regular_sale_code, payment_method_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table regular_sale_payment is '定期便支払方法';

comment on column regular_sale_payment.shop_code is 'ショップコード';

comment on column regular_sale_payment.regular_sale_code is '定期便コード';

comment on column regular_sale_payment.payment_method_no is '支払方法番号';

comment on column regular_sale_payment.orm_rowid is 'データ行ID';

comment on column regular_sale_payment.created_user is '作成ユーザ';

comment on column regular_sale_payment.created_datetime is '作成日時';

comment on column regular_sale_payment.updated_user is '更新ユーザ';

comment on column regular_sale_payment.updated_datetime is '更新日時';

create unique index regular_sale_payment_ix0
on regular_sale_payment
(orm_rowid)
tablespace ts_commodity_i01;
