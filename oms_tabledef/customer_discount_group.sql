create table customer_discount_group
(
    customer_group_code            varchar(16) not null,
    customer_group_name            varchar(40) not null,
    shipping_charge_free_flg       numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_discount_group_pk primary key (customer_group_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_discount_group is '顧客値引グループ';

comment on column customer_discount_group.customer_group_code is '顧客グループコード';

comment on column customer_discount_group.customer_group_name is '顧客グループ名称';

comment on column customer_discount_group.shipping_charge_free_flg is '送料無料フラグ';

comment on column customer_discount_group.orm_rowid is 'データ行ID';

comment on column customer_discount_group.created_user is '作成ユーザ';

comment on column customer_discount_group.created_datetime is '作成日時';

comment on column customer_discount_group.updated_user is '更新ユーザ';

comment on column customer_discount_group.updated_datetime is '更新日時';

create unique index customer_discount_group_ix0
on customer_discount_group
(orm_rowid)
tablespace ts_customer_i01;
