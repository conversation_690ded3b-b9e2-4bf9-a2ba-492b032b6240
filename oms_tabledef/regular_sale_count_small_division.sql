create table regular_sale_count_small_division
(
    customer_code                  varchar(16) not null,
    commodity_subsubcategory_code  varchar(16) not null,
    buy_count                      numeric(5,0) not null,
    regular_kaiji                  numeric(5,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_count_small_division_pk primary key (customer_code, commodity_subsubcategory_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table regular_sale_count_small_division is '定期回次小分類';

comment on column regular_sale_count_small_division.customer_code is '顧客コード';

comment on column regular_sale_count_small_division.commodity_subsubcategory_code is '商品小分類';

comment on column regular_sale_count_small_division.buy_count is '購入回数';

comment on column regular_sale_count_small_division.regular_kaiji is '定期回次';

comment on column regular_sale_count_small_division.orm_rowid is 'データ行ID';

comment on column regular_sale_count_small_division.created_user is '作成ユーザ';

comment on column regular_sale_count_small_division.created_datetime is '作成日時';

comment on column regular_sale_count_small_division.updated_user is '更新ユーザ';

comment on column regular_sale_count_small_division.updated_datetime is '更新日時';

create unique index regular_sale_count_small_division_ix0
on regular_sale_count_small_division
(orm_rowid)
tablespace ts_customer_i01;
