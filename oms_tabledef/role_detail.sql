create table role_detail
(
    shop_code                      varchar(16) not null,
    role_id                        varchar(8) not null,
    permission_code                varchar(6) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint role_detail_pk primary key (shop_code, role_id, permission_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table role_detail is 'ロール詳細';

comment on column role_detail.shop_code is 'ショップコード';

comment on column role_detail.role_id is 'ロールID';

comment on column role_detail.permission_code is '権限コード';

comment on column role_detail.orm_rowid is 'データ行ID';

comment on column role_detail.created_user is '作成ユーザ';

comment on column role_detail.created_datetime is '作成日時';

comment on column role_detail.updated_user is '更新ユーザ';

comment on column role_detail.updated_datetime is '更新日時';

create unique index role_detail_ix0
on role_detail
(orm_rowid)
tablespace ts_shop_i01;
