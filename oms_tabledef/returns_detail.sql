create table returns_detail
(
    henpin_request_no              varchar(10) not null,
    shipping_no                    varchar(16) not null,
    shipping_detail_no             numeric(16,0) not null,
    order_no                       varchar(16) not null,
    order_detail_no                numeric(16,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    commodity_name                 varchar(100) not null,
    commodity_kind                 varchar(2) not null,
    baitai_code                    varchar(10) not null,
    baitai_name                    varchar(50) not null,
    hinban_code                    varchar(24) not null,
    benefits_code                  varchar(10),
    benefits_name                  varchar(50),
    henpin_qt                      numeric(5,0) not null,
    regular_contract_no            varchar(14),
    unit_price                     numeric(8,0) not null,
    discount_price                 numeric(8,0),
    discount_amount                numeric(8,0),
    retail_price                   numeric(8,0) not null,
    retail_tax_group_code          varchar(8) not null,
    retail_tax_no                  numeric(3,0) not null,
    retail_tax_rate                numeric(3,0) not null,
    retail_tax                     numeric(10,2) not null,
    commodity_tax                  numeric(10,2) not null,
    commodity_tax_type             numeric(1,0) not null,
    purchasing_amount              numeric(8,0) not null,
    henpin_price                   numeric(8,0) not null,
    henpin_yoyaku_qt               numeric(5,0) not null,
    change_qt                      numeric(5,0) not null,
    henpin_support_kind            varchar(2),
    wms_henpin_rireki_no           varchar(10),
    copy_soko_shiji                varchar(40),
    grant_plan_point_prod_detail   numeric(10,0),
    reduction_plan_point_prod_detail numeric(10,0),
    campaign_instructions_code     varchar(16),
    campaign_instructions_name     varchar(50),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint returns_detail_pk primary key (henpin_request_no, shipping_no, shipping_detail_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table returns_detail is '返品明細';

comment on column returns_detail.henpin_request_no is '返品依頼番号';

comment on column returns_detail.shipping_no is '出荷番号';

comment on column returns_detail.shipping_detail_no is '出荷明細番号';

comment on column returns_detail.order_no is '受注番号';

comment on column returns_detail.order_detail_no is '受注明細番号';

comment on column returns_detail.shop_code is 'ショップコード';

comment on column returns_detail.sku_code is 'SKUコード';

comment on column returns_detail.commodity_code is '商品コード';

comment on column returns_detail.commodity_name is '商品名称';

comment on column returns_detail.commodity_kind is '商品種別';

comment on column returns_detail.baitai_code is '媒体コード';

comment on column returns_detail.baitai_name is '媒体名称';

comment on column returns_detail.hinban_code is '品番コード';

comment on column returns_detail.benefits_code is '特典コード';

comment on column returns_detail.benefits_name is '特典名称';

comment on column returns_detail.henpin_qt is '返品数量';

comment on column returns_detail.regular_contract_no is '定期契約番号';

comment on column returns_detail.unit_price is '商品単価';

comment on column returns_detail.discount_price is '特別価格';

comment on column returns_detail.discount_amount is '値引額';

comment on column returns_detail.retail_price is '販売価格';

comment on column returns_detail.retail_tax_group_code is '販売時消費税グループコード';

comment on column returns_detail.retail_tax_no is '販売時消費税番号';

comment on column returns_detail.retail_tax_rate is '販売時消費税率';

comment on column returns_detail.retail_tax is '販売時消費税額';

comment on column returns_detail.commodity_tax is '商品消費税額';

comment on column returns_detail.commodity_tax_type is '商品消費税区分';

comment on column returns_detail.purchasing_amount is '購入商品数';

comment on column returns_detail.henpin_price is '返品額';

comment on column returns_detail.henpin_yoyaku_qt is '今回返品数量';

comment on column returns_detail.change_qt is '今回交換数量';

comment on column returns_detail.henpin_support_kind is '返品対応種別';

comment on column returns_detail.wms_henpin_rireki_no is 'WMS返品履歴番号';

comment on column returns_detail.copy_soko_shiji is '再発送倉庫指示';

comment on column returns_detail.grant_plan_point_prod_detail is '返品前付与予定ポイント数';

comment on column returns_detail.reduction_plan_point_prod_detail is '返品前利用予定ポイント数';

comment on column returns_detail.campaign_instructions_code is 'キャンペーン設定コード';

comment on column returns_detail.campaign_instructions_name is 'キャンペーン設定名称';

comment on column returns_detail.orm_rowid is 'データ行ID';

comment on column returns_detail.created_user is '作成ユーザ';

comment on column returns_detail.created_datetime is '作成日時';

comment on column returns_detail.updated_user is '更新ユーザ';

comment on column returns_detail.updated_datetime is '更新日時';

create unique index returns_detail_ix0
on returns_detail
(orm_rowid)
tablespace ts_order_i01;

create index returns_detail_ix1
on returns_detail
(order_no, order_detail_no)
tablespace ts_order_i01;

create index returns_detail_ix2
on returns_detail
(hinban_code)
tablespace ts_order_i01;

create index returns_detail_ix3
on returns_detail
(wms_henpin_rireki_no)
tablespace ts_order_i01;
