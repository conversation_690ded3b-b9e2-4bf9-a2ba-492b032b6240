create table media_division
(
    media_group                    varchar(1) not null,
    media_kbn_code                 varchar(15) not null,
    media_kbn_name                 varchar(100) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint media_division_pk primary key (media_group, media_kbn_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table media_division is 'メディア区分';

comment on column media_division.media_group is 'メディア分類';

comment on column media_division.media_kbn_code is 'メディア区分コード';

comment on column media_division.media_kbn_name is 'メディア区分名称';

comment on column media_division.orm_rowid is 'データ行ID';

comment on column media_division.created_user is '作成ユーザ';

comment on column media_division.created_datetime is '作成日時';

comment on column media_division.updated_user is '更新ユーザ';

comment on column media_division.updated_datetime is '更新日時';

create unique index media_division_ix0
on media_division
(orm_rowid)
tablespace ts_shop_i01;
