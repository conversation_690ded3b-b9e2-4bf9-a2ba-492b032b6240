create table standard_detail
(
    shop_code                      varchar(16) not null,
    standard_code                  varchar(16) not null,
    standard_detail_code           varchar(16) not null,
    standard_detail_name           varchar(20) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint standard_detail_pk primary key (shop_code, standard_code, standard_detail_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table standard_detail is '規格詳細';

comment on column standard_detail.shop_code is 'ショップコード';

comment on column standard_detail.standard_code is '規格コード';

comment on column standard_detail.standard_detail_code is '規格詳細コード';

comment on column standard_detail.standard_detail_name is '規格詳細名称';

comment on column standard_detail.display_order is '表示順';

comment on column standard_detail.orm_rowid is 'データ行ID';

comment on column standard_detail.created_user is '作成ユーザ';

comment on column standard_detail.created_datetime is '作成日時';

comment on column standard_detail.updated_user is '更新ユーザ';

comment on column standard_detail.updated_datetime is '更新日時';

create unique index standard_detail_ix0
on standard_detail
(orm_rowid)
tablespace ts_commodity_i01;

create unique index standard_detail_lpk
on standard_detail
(lower(shop_code), lower(standard_code), lower(standard_detail_code))
tablespace ts_commodity_i01;
