create table point_history
(
    point_history_id               numeric(38,0) not null,
    point_issue_datetime           timestamp(0) not null,
    customer_code                  varchar(16) not null,
    point_issue_status             numeric(1,0) not null,
    point_issue_type               numeric(1,0) not null,
    point_type                     numeric(1,0) not null,
    order_no                       varchar(16),
    point_use_start_date           timestamp(0),
    point_use_end_date             timestamp(0),
    review_id                      numeric(38,0),
    enquete_code                   varchar(16),
    description                    varchar(100),
    issued_point                   numeric(9,0) not null,
    shop_code                      varchar(16) not null,
    point_used_datetime            timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint point_history_pk primary key (point_history_id) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table point_history is 'ポイント履歴';

comment on column point_history.point_history_id is 'ポイント履歴ID';

comment on column point_history.point_issue_datetime is 'ポイント発行日時';

comment on column point_history.customer_code is '顧客コード';

comment on column point_history.point_issue_status is 'ポイント発行ステータス';

comment on column point_history.point_issue_type is 'ポイント発行区分';

comment on column point_history.point_type is 'ポイント区分';

comment on column point_history.order_no is '受注番号';

comment on column point_history.point_use_start_date is 'ポイント利用期間開始日';

comment on column point_history.point_use_end_date is 'ポイント利用期間終了日';

comment on column point_history.review_id is 'レビューID';

comment on column point_history.enquete_code is 'アンケートコード';

comment on column point_history.description is 'ポイント付与行使理由';

comment on column point_history.issued_point is '発行ポイント';

comment on column point_history.shop_code is 'ショップコード';

comment on column point_history.point_used_datetime is 'ポイント利用日時';

comment on column point_history.orm_rowid is 'データ行ID';

comment on column point_history.created_user is '作成ユーザ';

comment on column point_history.created_datetime is '作成日時';

comment on column point_history.updated_user is '更新ユーザ';

comment on column point_history.updated_datetime is '更新日時';

create unique index point_history_ix0
on point_history
(orm_rowid)
tablespace ts_customer_i01;

create index point_history_ix1
on point_history
(customer_code)
tablespace ts_customer_i01;
