create table work_shipping_instructions_monitor
(
    accept_no                      varchar(22) not null,
    seq                            numeric(3,0) not null,
    message                        varchar(1000),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_shipping_instructions_monitor_pk primary key (accept_no, seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_shipping_instructions_monitor is '出荷指示注文監視ワーク';

comment on column work_shipping_instructions_monitor.accept_no is '基幹出荷指示番号';

comment on column work_shipping_instructions_monitor.seq is '連番';

comment on column work_shipping_instructions_monitor.message is 'メッセージ';

comment on column work_shipping_instructions_monitor.orm_rowid is 'データ行ID';

comment on column work_shipping_instructions_monitor.created_user is '作成ユーザ';

comment on column work_shipping_instructions_monitor.created_datetime is '作成日時';

comment on column work_shipping_instructions_monitor.updated_user is '更新ユーザ';

comment on column work_shipping_instructions_monitor.updated_datetime is '更新日時';

create unique index work_shipping_instructions_monitor_ix0
on work_shipping_instructions_monitor
(orm_rowid)
tablespace ts_order_i01;
