create table card_info_history
(
    card_change_kanri_no           numeric(38,0) not null,
    customer_code                  varchar(16) not null,
    credit_card_kanri_no           varchar(12) not null,
    credit_card_kanri_detail_no    varchar(4) not null,
    credit_card_no                 varchar(50) not null,
    card_expire_year               varchar(4),
    card_expire_month              varchar(2),
    card_holder                    varchar(150),
    card_brand                     varchar(2) not null,
    default_use_flag               numeric(1,0) not null,
    change_datetime                timestamp(0) not null,
    change_channel_kbn             varchar(2) not null,
    card_keep_ng_flg               numeric(1,0) not null,
    change_reason_kbn              varchar(2) not null,
    change_reason                  varchar(200) not null,
    change_user_code               numeric(38,0),
    dhc_card_flg                   numeric(1,0) not null,
    crm_card_id                    varchar(20),
    crm_card_updated_datetime      varchar(24),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint card_info_history_pk primary key (card_change_kanri_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table card_info_history is 'カード情報履歴';

comment on column card_info_history.card_change_kanri_no is 'カード変更管理番号';

comment on column card_info_history.customer_code is '顧客コード';

comment on column card_info_history.credit_card_kanri_no is 'クレジットカードお預かり管理番号';

comment on column card_info_history.credit_card_kanri_detail_no is 'クレジットカードお預かり管理明細番号';

comment on column card_info_history.credit_card_no is 'クレジットカード番号';

comment on column card_info_history.card_expire_year is '有効期限年';

comment on column card_info_history.card_expire_month is '有効期限月';

comment on column card_info_history.card_holder is '名義人';

comment on column card_info_history.card_brand is 'カードブランド';

comment on column card_info_history.default_use_flag is '通常使用フラグ';

comment on column card_info_history.change_datetime is '変更日時';

comment on column card_info_history.change_channel_kbn is '顧客情報変更経路';

comment on column card_info_history.card_keep_ng_flg is 'お預かり不可フラグ';

comment on column card_info_history.change_reason_kbn is '変更理由区分';

comment on column card_info_history.change_reason is '変更理由';

comment on column card_info_history.change_user_code is '変更担当者ユーザコード';

comment on column card_info_history.dhc_card_flg is 'DHCカードフラグ';

comment on column card_info_history.crm_card_id is 'CRMカード情報ID';

comment on column card_info_history.crm_card_updated_datetime is 'CRMカード情報更新日時';

comment on column card_info_history.orm_rowid is 'データ行ID';

comment on column card_info_history.created_user is '作成ユーザ';

comment on column card_info_history.created_datetime is '作成日時';

comment on column card_info_history.updated_user is '更新ユーザ';

comment on column card_info_history.updated_datetime is '更新日時';

