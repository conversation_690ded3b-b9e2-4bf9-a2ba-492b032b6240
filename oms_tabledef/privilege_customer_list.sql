create table privilege_customer_list
(
    benefits_code                  varchar(10) not null,
    customer_code                  varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint privilege_customer_list_pk primary key (benefits_code, customer_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table privilege_customer_list is '特典顧客リスト';

comment on column privilege_customer_list.benefits_code is '特典コード';

comment on column privilege_customer_list.customer_code is '顧客コード';

comment on column privilege_customer_list.orm_rowid is 'データ行ID';

comment on column privilege_customer_list.created_user is '作成ユーザ';

comment on column privilege_customer_list.created_datetime is '作成日時';

comment on column privilege_customer_list.updated_user is '更新ユーザ';

comment on column privilege_customer_list.updated_datetime is '更新日時';

create unique index privilege_customer_list_ix0
on privilege_customer_list
(orm_rowid)
tablespace ts_shop_i01;
