create table delivery_type
(
    shop_code                      varchar(16) not null,
    delivery_type_no               numeric(8,0) not null,
    delivery_type_name             varchar(40) not null,
    delivery_specification_type    numeric(1,0),
    shipping_charge_tax_type       numeric(1,0) not null,
    arrangement_time               numeric(2,0) not null,
    delay_hours_of_arrangement     numeric(2,0) not null,
    parcel_url                     varchar(128),
    parcel_url_sp                  varchar(128),
    shipping_charge_free_flg       numeric(1,0),
    shipping_charge_free_threshold numeric(8,0),
    shipping_charge_flg            numeric(1,0),
    shipping_charge_threshold      numeric(8,0),
    display_flg                    numeric(1,0) not null,
    shipping_method                varchar(2) not null,
    delivery_carrier_name          varchar(40),
    delivery_carrier_url           varchar(128),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint delivery_type_pk primary key (shop_code, delivery_type_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table delivery_type is '配送種別';

comment on column delivery_type.shop_code is 'ショップコード';

comment on column delivery_type.delivery_type_no is '配送種別番号';

comment on column delivery_type.delivery_type_name is '配送種別名称';

comment on column delivery_type.delivery_specification_type is '配送詳細区分';

comment on column delivery_type.shipping_charge_tax_type is '送料消費税区分';

comment on column delivery_type.arrangement_time is '手配日数';

comment on column delivery_type.delay_hours_of_arrangement is '手配猶予時間';

comment on column delivery_type.parcel_url is '宅配業者URL';

comment on column delivery_type.parcel_url_sp is 'スマートフォン宅配業者URL';

comment on column delivery_type.shipping_charge_free_flg is '送料無料フラグ';

comment on column delivery_type.shipping_charge_free_threshold is '送料無料閾値';

comment on column delivery_type.shipping_charge_flg is '送料課金フラグ';

comment on column delivery_type.shipping_charge_threshold is '送料課金閾値';

comment on column delivery_type.display_flg is '表示フラグ';

comment on column delivery_type.shipping_method is '配送方法';

comment on column delivery_type.delivery_carrier_name is '配送業者名称';

comment on column delivery_type.delivery_carrier_url is '配送業者URL';

comment on column delivery_type.orm_rowid is 'データ行ID';

comment on column delivery_type.created_user is '作成ユーザ';

comment on column delivery_type.created_datetime is '作成日時';

comment on column delivery_type.updated_user is '更新ユーザ';

comment on column delivery_type.updated_datetime is '更新日時';

create unique index delivery_type_ix0
on delivery_type
(orm_rowid)
tablespace ts_shop_i01;

create unique index delivery_type_ix1
on delivery_type
(delivery_type_no, shop_code, display_flg)
tablespace ts_shop_i01;
