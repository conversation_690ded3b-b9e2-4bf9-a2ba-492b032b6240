create table enquete_reply_input
(
    enquete_code                   varchar(16) not null,
    enquete_question_no            numeric(8,0) not null,
    customer_code                  varchar(16) not null,
    enquete_reply                  text,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint enquete_reply_input_pk primary key (enquete_code, enquete_question_no, customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table enquete_reply_input is 'アンケート回答入力式';

comment on column enquete_reply_input.enquete_code is 'アンケートコード';

comment on column enquete_reply_input.enquete_question_no is 'アンケート設問番号';

comment on column enquete_reply_input.customer_code is '顧客コード';

comment on column enquete_reply_input.enquete_reply is 'アンケート回答';

comment on column enquete_reply_input.orm_rowid is 'データ行ID';

comment on column enquete_reply_input.created_user is '作成ユーザ';

comment on column enquete_reply_input.created_datetime is '作成日時';

comment on column enquete_reply_input.updated_user is '更新ユーザ';

comment on column enquete_reply_input.updated_datetime is '更新日時';

create unique index enquete_reply_input_ix0
on enquete_reply_input
(orm_rowid)
tablespace ts_customer_i01;
