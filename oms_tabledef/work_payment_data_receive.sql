create table work_payment_data_receive
(
    bill_no                        varchar(17),
    payment_method_kbn             varchar(2),
    prompt_confirm_kbn             varchar(1),
    payment_price                  numeric(10,0),
    customer_payment_datetime      timestamp(0),
    payment_confirm_date           timestamp(0),
    payment_memo                   varchar(1000),
    payment_notice_no              numeric(10,0),
    reflect_flg                    numeric(1,0) not null
)
tablespace ts_shop_t01;

comment on table work_payment_data_receive is '入金データ受信ワーク';

comment on column work_payment_data_receive.bill_no is '請求番号';

comment on column work_payment_data_receive.payment_method_kbn is '入金方法区分';

comment on column work_payment_data_receive.prompt_confirm_kbn is '速報確定区分';

comment on column work_payment_data_receive.payment_price is '入金額';

comment on column work_payment_data_receive.customer_payment_datetime is 'お客様入金日時';

comment on column work_payment_data_receive.payment_confirm_date is '入金確認日';

comment on column work_payment_data_receive.payment_memo is '入金メモ';

comment on column work_payment_data_receive.payment_notice_no is '入金通知シーケンス';

comment on column work_payment_data_receive.reflect_flg is '反映フラグ';

