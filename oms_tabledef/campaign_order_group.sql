create table campaign_order_group
(
    campaign_instructions_code     varchar(16) not null,
    campaign_group_no              numeric(8,0) not null,
    campaign_joken_disp            varchar(50),
    exclude_joken_disp             varchar(50),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_order_group_pk primary key (campaign_instructions_code, campaign_group_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_order_group is 'キャンペーン設定条件グループ';

comment on column campaign_order_group.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_order_group.campaign_group_no is 'キャンペーン設定グループ番号';

comment on column campaign_order_group.campaign_joken_disp is 'キャンペーン条件（一覧表示用）';

comment on column campaign_order_group.exclude_joken_disp is 'キャンペーン除外条件（一覧表示用）';

comment on column campaign_order_group.orm_rowid is 'データ行ID';

comment on column campaign_order_group.created_user is '作成ユーザ';

comment on column campaign_order_group.created_datetime is '作成日時';

comment on column campaign_order_group.updated_user is '更新ユーザ';

comment on column campaign_order_group.updated_datetime is '更新日時';

create unique index campaign_order_group_ix0
on campaign_order_group
(orm_rowid)
tablespace ts_commodity_i01;
