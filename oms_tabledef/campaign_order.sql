create table campaign_order
(
    campaign_instructions_code     varchar(16) not null,
    campaign_group_no              numeric(8,0) not null,
    joken_type                     varchar(1) not null,
    campaign_joken_no              numeric(8,0) not null,
    joken_kind1                    varchar(1),
    joken_kind2                    varchar(3),
    joken                          varchar(16),
    joken_min                      numeric(8,0),
    joken_max                      numeric(8,0),
    regular_kaiji                  numeric(5,0),
    joken_month_num                numeric(3,0),
    commodity_name                 varchar(100),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_order_pk primary key (campaign_instructions_code, campaign_group_no, joken_type, campaign_joken_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_order is 'キャンペーン設定条件';

comment on column campaign_order.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_order.campaign_group_no is 'キャンペーン設定グループ番号';

comment on column campaign_order.joken_type is '条件分類';

comment on column campaign_order.campaign_joken_no is 'キャンペーン設定条件番号';

comment on column campaign_order.joken_kind1 is '条件種別１';

comment on column campaign_order.joken_kind2 is '条件種別２';

comment on column campaign_order.joken is '条件内容';

comment on column campaign_order.joken_min is '条件内容下限';

comment on column campaign_order.joken_max is '条件内容上限';

comment on column campaign_order.regular_kaiji is '定期回次';

comment on column campaign_order.joken_month_num is '期限月数';

comment on column campaign_order.commodity_name is '商品名称';

comment on column campaign_order.orm_rowid is 'データ行ID';

comment on column campaign_order.created_user is '作成ユーザ';

comment on column campaign_order.created_datetime is '作成日時';

comment on column campaign_order.updated_user is '更新ユーザ';

comment on column campaign_order.updated_datetime is '更新日時';

create unique index campaign_order_ix0
on campaign_order
(orm_rowid)
tablespace ts_commodity_i01;
