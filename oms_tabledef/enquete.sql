create table enquete
(
    enquete_code                   varchar(16) not null,
    enquete_name                   varchar(40) not null,
    enquete_start_date             timestamp(0),
    enquete_end_date               timestamp(0),
    enquete_invest_point           numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint enquete_pk primary key (enquete_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table enquete is 'アンケート';

comment on column enquete.enquete_code is 'アンケートコード';

comment on column enquete.enquete_name is 'アンケート名称';

comment on column enquete.enquete_start_date is 'アンケート開始日';

comment on column enquete.enquete_end_date is 'アンケート終了日';

comment on column enquete.enquete_invest_point is 'アンケート付与ポイント数';

comment on column enquete.orm_rowid is 'データ行ID';

comment on column enquete.created_user is '作成ユーザ';

comment on column enquete.created_datetime is '作成日時';

comment on column enquete.updated_user is '更新ユーザ';

comment on column enquete.updated_datetime is '更新日時';

create unique index enquete_ix0
on enquete
(orm_rowid)
tablespace ts_customer_i01;
