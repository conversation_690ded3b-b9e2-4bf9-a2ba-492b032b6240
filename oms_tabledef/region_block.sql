create table region_block
(
    shop_code                      varchar(16) not null,
    region_block_id                numeric(38,0) not null,
    region_block_name              varchar(20) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint region_block_pk primary key (shop_code, region_block_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table region_block is '地域ブロック';

comment on column region_block.shop_code is 'ショップコード';

comment on column region_block.region_block_id is '地域ブロックID';

comment on column region_block.region_block_name is '地域ブロック名称';

comment on column region_block.orm_rowid is 'データ行ID';

comment on column region_block.created_user is '作成ユーザ';

comment on column region_block.created_datetime is '作成日時';

comment on column region_block.updated_user is '更新ユーザ';

comment on column region_block.updated_datetime is '更新日時';

create unique index region_block_ix0
on region_block
(orm_rowid)
tablespace ts_shop_i01;
