create table stock_status
(
    shop_code                      varchar(16) not null,
    stock_status_no                numeric(8,0) not null,
    stock_status_name              varchar(40),
    stock_sufficient_message       varchar(50),
    stock_little_message           varchar(50),
    out_of_stock_message           varchar(50),
    stock_sufficient_threshold     numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint stock_status_pk primary key (shop_code, stock_status_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table stock_status is '在庫状況';

comment on column stock_status.shop_code is 'ショップコード';

comment on column stock_status.stock_status_no is '在庫状況番号';

comment on column stock_status.stock_status_name is '在庫状況分類名';

comment on column stock_status.stock_sufficient_message is '在庫多メッセージ';

comment on column stock_status.stock_little_message is '在庫少メッセージ';

comment on column stock_status.out_of_stock_message is '在庫切メッセージ';

comment on column stock_status.stock_sufficient_threshold is '在庫多閾値';

comment on column stock_status.orm_rowid is 'データ行ID';

comment on column stock_status.created_user is '作成ユーザ';

comment on column stock_status.created_datetime is '作成日時';

comment on column stock_status.updated_user is '更新ユーザ';

comment on column stock_status.updated_datetime is '更新日時';

create unique index stock_status_ix0
on stock_status
(orm_rowid)
tablespace ts_commodity_i01;
