create table order_header
(
    order_no                       varchar(16) not null,
    shop_code                      varchar(16) not null,
    order_datetime                 timestamp(0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    guest_flg                      numeric(1,0) not null,
    last_name                      varchar(20) not null,
    first_name                     varchar(20),
    last_name_kana                 varchar(40) not null,
    first_name_kana                varchar(40),
    email                          varchar(256),
    birth_date                     timestamp(0) not null,
    sex                            numeric(1,0) not null,
    postal_code                    varchar(7) not null,
    prefecture_code                varchar(2) not null,
    address1                       varchar(4) not null,
    address2                       varchar(50) not null,
    address3                       varchar(255) not null,
    address4                       varchar(100),
    corporation_post_name          varchar(40),
    phone_number                   varchar(16) not null,
    advance_later_flg              numeric(1,0) not null,
    payment_method_no              numeric(8,0) not null,
    payment_method_type            varchar(2) not null,
    payment_method_name            varchar(25),
    ext_payment_method_type        varchar(2) not null,
    payment_commission             numeric(8,0) not null,
    payment_commission_tax_gr_code varchar(8) not null,
    payment_commission_tax_no      numeric(3,0) not null,
    payment_commission_tax_rate    numeric(3,0) not null,
    payment_commission_tax         numeric(10,2) not null,
    payment_commission_tax_type    numeric(1,0) not null,
    coupon_management_code         varchar(16),
    coupon_code                    varchar(16),
    coupon_name                    varchar(50),
    coupon_type                    numeric(1,0),
    coupon_use_purchase_price      numeric(8,0),
    coupon_discount_type           numeric(1,0),
    coupon_discount_price          numeric(8,0),
    coupon_discount_rate           numeric(3,0),
    coupon_used_amount             numeric(8,0) not null,
    coupon_start_datetime          timestamp(0),
    coupon_end_datetime            timestamp(0),
    coupon_kbn                     varchar(2),
    goods_group                    varchar(16),
    commodity_category_code        varchar(16),
    commodity_series               varchar(5),
    coupon_commodity_code_display  varchar(20),
    baitai_name                    varchar(50),
    used_point                     numeric(8,0),
    total_amount                   numeric(8,0) not null,
    ec_promotion_id                varchar(256),
    ec_promotion_name              varchar(256),
    ec_promotion_discount_price    numeric(8,0),
    ec_campaign_id                 varchar(256),
    ec_campaign_name               varchar(256),
    payment_date                   timestamp(0),
    payment_limit_date             timestamp(0),
    payment_status                 numeric(1,0) not null,
    ext_payment_status             varchar(2) not null,
    customer_group_code            varchar(16),
    data_transport_status          numeric(1,0) not null,
    order_status                   numeric(1,0) not null,
    ext_order_status               varchar(2) not null,
    tax_reference_date             timestamp(0) not null,
    cancel_date                    timestamp(0),
    client_group                   varchar(2) not null,
    caution                        varchar(200),
    message                        varchar(200),
    payment_order_id               varchar(100),
    cvs_code                       varchar(2),
    payment_receipt_no             varchar(64),
    payment_receipt_url            varchar(500),
    receipt_no                     varchar(20),
    customer_no                    varchar(50),
    confirm_no                     varchar(50),
    career_key                     varchar(1),
    order_create_error_code        varchar(8) not null,
    order_display_status           numeric(1,0) not null,
    order_kind_kbn                 varchar(1) not null,
    marketing_channel              varchar(2) not null,
    original_order_no              varchar(16),
    external_order_no              varchar(50),
    order_recieve_datetime         timestamp(0) not null,
    order_update_datetime          timestamp(0) not null,
    order_update_reason_kbn        varchar(2),
    cancel_reason_kbn              varchar(2),
    uncollectible_date             timestamp(0),
    order_total_price              numeric(10,0) not null,
    account_receivable_balance     numeric(10,0) not null,
    appropriate_amount             numeric(10,0) not null,
    bill_address_kbn               varchar(1) not null,
    receipt_flg                    numeric(1,0) not null,
    receipt_to                     varchar(50),
    receipt_detail                 varchar(50),
    bill_price                     numeric(10,0) not null,
    bill_no                        varchar(17),
    bill_print_count               numeric(3,0) not null,
    authority_result_kbn           varchar(1),
    authority_no                   varchar(32),
    card_password                  varchar(32),
    authority_approval_no          varchar(10),
    authority_date                 timestamp(0),
    authority_price                numeric(10,0),
    authority_cancel_approval_no   varchar(10),
    authority_cancel_date          timestamp(0),
    credit_payment_no              varchar(10),
    credit_payment_date            timestamp(0),
    credit_payment_price           numeric(10,0),
    credit_cancel_payment_no       varchar(10),
    credit_cancel_payment_date     timestamp(0),
    credit_result_kbn              varchar(1),
    card_brand                     varchar(2),
    credit_card_kanri_no           varchar(12),
    credit_card_kanri_detail_no    varchar(4),
    credit_card_no                 varchar(50),
    credit_card_meigi              varchar(150),
    credit_card_valid_year         varchar(4),
    credit_card_valid_month        varchar(2),
    credit_card_pay_count          varchar(2),
    payment_bar_code               varchar(44),
    amzn_charge_permission_id      text,
    amzn_charge_id                 text,
    amzn_charge_status             varchar(2),
    amzn_authorization_datetime    timestamp(0),
    amzn_capture_initiated_datetime timestamp(0),
    amzn_captured_datetime         timestamp(0),
    amzn_canceled_datetime         timestamp(0),
    order_user_code                numeric(38,0),
    order_user                     varchar(20),
    change_user_code               numeric(38,0),
    change_user                    varchar(20),
    demand_kbn                     varchar(1) not null,
    demand1_ref_date               timestamp(0),
    demand1_date                   timestamp(0),
    demand1_limit_date             timestamp(0),
    demand1_amount                 numeric(8,0),
    demand1_bar_code               varchar(44),
    demand2_ref_date               timestamp(0),
    demand2_date                   timestamp(0),
    demand2_limit_date             timestamp(0),
    demand2_amount                 numeric(8,0),
    demand2_bar_code               varchar(44),
    demand3_ref_date               timestamp(0),
    demand3_date                   timestamp(0),
    demand3_limit_date             timestamp(0),
    demand3_amount                 numeric(8,0),
    demand3_bar_code               varchar(44),
    kashidaore_date                timestamp(0),
    demand_exclude_reason_kbn      varchar(2),
    demand_exclude_start_date      timestamp(0),
    demand_exclude_end_date        timestamp(0),
    bill_sei_kj                    varchar(50) not null,
    bill_mei_kj                    varchar(50),
    bill_sei_kn                    varchar(100) not null,
    bill_mei_kn                    varchar(100),
    bill_tel_no                    varchar(16) not null,
    bill_zipcd                     varchar(7) not null,
    bill_addr1                     varchar(4) not null,
    bill_addr2                     varchar(50) not null,
    bill_addr3                     varchar(255) not null,
    bill_addr4                     varchar(100),
    bill_corporation_post_name     varchar(40),
    nohinsyo_uketsuke_tanto        varchar(300),
    grant_plan_point_prod          numeric(10,0),
    grant_plan_point_other         numeric(10,0),
    grant_plan_point_total         numeric(10,0),
    grant_point_prod               numeric(10,0),
    grant_point_other              numeric(10,0),
    grant_point_total              numeric(10,0),
    reduction_plan_point_total     numeric(10,0),
    reduction_point_total          numeric(10,0),
    subtotal_before_campaign       numeric(10,0),
    subtotal_after_campaign        numeric(10,0),
    total_before_campaign          numeric(10,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_header_pk primary key (order_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_header is '受注ヘッダ';

comment on column order_header.order_no is '受注番号';

comment on column order_header.shop_code is 'ショップコード';

comment on column order_header.order_datetime is '受注日時';

comment on column order_header.customer_code is '顧客コード';

comment on column order_header.neo_customer_no is '顧客番号';

comment on column order_header.guest_flg is 'ゲストフラグ';

comment on column order_header.last_name is '姓';

comment on column order_header.first_name is '名';

comment on column order_header.last_name_kana is '姓かな';

comment on column order_header.first_name_kana is '名かな';

comment on column order_header.email is 'メールアドレス';

comment on column order_header.birth_date is '生年月日';

comment on column order_header.sex is '性別';

comment on column order_header.postal_code is '郵便番号';

comment on column order_header.prefecture_code is '都道府県コード';

comment on column order_header.address1 is '住所1';

comment on column order_header.address2 is '住所2';

comment on column order_header.address3 is '住所3';

comment on column order_header.address4 is '住所4';

comment on column order_header.corporation_post_name is '会社部署名';

comment on column order_header.phone_number is '電話番号';

comment on column order_header.advance_later_flg is '先後払フラグ';

comment on column order_header.payment_method_no is '支払方法番号';

comment on column order_header.payment_method_type is '支払方法区分';

comment on column order_header.payment_method_name is '支払方法名称';

comment on column order_header.ext_payment_method_type is '支払方法区分（拡張）';

comment on column order_header.payment_commission is '支払手数料';

comment on column order_header.payment_commission_tax_gr_code is '支払手数料消費税グループコード';

comment on column order_header.payment_commission_tax_no is '支払手数料消費税番号';

comment on column order_header.payment_commission_tax_rate is '支払手数料消費税率';

comment on column order_header.payment_commission_tax is '支払手数料消費税額';

comment on column order_header.payment_commission_tax_type is '支払手数料消費税区分';

comment on column order_header.coupon_management_code is 'クーポン管理コード';

comment on column order_header.coupon_code is 'クーポンコード';

comment on column order_header.coupon_name is 'クーポン名称';

comment on column order_header.coupon_type is 'クーポン種別';

comment on column order_header.coupon_use_purchase_price is 'クーポン利用最低購入金額';

comment on column order_header.coupon_discount_type is 'クーポン値引区分';

comment on column order_header.coupon_discount_price is 'クーポン値引額';

comment on column order_header.coupon_discount_rate is 'クーポン値引率';

comment on column order_header.coupon_used_amount is 'クーポン利用額';

comment on column order_header.coupon_start_datetime is 'クーポン開始日時';

comment on column order_header.coupon_end_datetime is 'クーポン終了日時';

comment on column order_header.coupon_kbn is 'クーポン区分';

comment on column order_header.goods_group is 'クーポン部門';

comment on column order_header.commodity_category_code is 'クーポン商品大分類';

comment on column order_header.commodity_series is 'クーポン商品シリーズ';

comment on column order_header.coupon_commodity_code_display is 'クーポン商品コード表示値';

comment on column order_header.baitai_name is 'クーポン媒体名称';

comment on column order_header.used_point is '利用ポイント';

comment on column order_header.total_amount is '支払合計金額';

comment on column order_header.ec_promotion_id is 'ECプロモーションID';

comment on column order_header.ec_promotion_name is 'ECプロモーション名';

comment on column order_header.ec_promotion_discount_price is 'ECプロモーション値引額';

comment on column order_header.ec_campaign_id is 'ECキャンペーンID';

comment on column order_header.ec_campaign_name is 'ECキャンペーン名';

comment on column order_header.payment_date is '入金日';

comment on column order_header.payment_limit_date is '支払期限日';

comment on column order_header.payment_status is '入金ステータス';

comment on column order_header.ext_payment_status is '入金ステータス（拡張）';

comment on column order_header.customer_group_code is '顧客グループコード';

comment on column order_header.data_transport_status is 'データ連携ステータス';

comment on column order_header.order_status is '受注ステータス';

comment on column order_header.ext_order_status is '受注ステータス（拡張）';

comment on column order_header.tax_reference_date is '消費税適用基準日';

comment on column order_header.cancel_date is 'キャンセル日';

comment on column order_header.client_group is 'クライアントグループ';

comment on column order_header.caution is '注意事項（管理側のみ参照）';

comment on column order_header.message is '連絡事項';

comment on column order_header.payment_order_id is '決済ID';

comment on column order_header.cvs_code is 'コンビニコード';

comment on column order_header.payment_receipt_no is '承認番号';

comment on column order_header.payment_receipt_url is '払込URL';

comment on column order_header.receipt_no is 'ATM決済収納機関番号';

comment on column order_header.customer_no is 'ATM決済お客様番号';

comment on column order_header.confirm_no is 'ATM決済確認番号';

comment on column order_header.career_key is '携帯キャリア種別区分';

comment on column order_header.order_create_error_code is '受注作成エラーコード';

comment on column order_header.order_display_status is '受注表示区分';

comment on column order_header.order_kind_kbn is '受注種別区分';

comment on column order_header.marketing_channel is '販売経路';

comment on column order_header.original_order_no is '元受注番号';

comment on column order_header.external_order_no is '外部受注番号';

comment on column order_header.order_recieve_datetime is '受注受付日時';

comment on column order_header.order_update_datetime is '受注変更日時';

comment on column order_header.order_update_reason_kbn is '受注変更理由区分';

comment on column order_header.cancel_reason_kbn is 'キャンセル理由区分';

comment on column order_header.uncollectible_date is '回収不能処理日';

comment on column order_header.order_total_price is '注文合計額';

comment on column order_header.account_receivable_balance is '売掛残金';

comment on column order_header.appropriate_amount is '充当金額';

comment on column order_header.bill_address_kbn is '請求先区分';

comment on column order_header.receipt_flg is '領収書フラグ';

comment on column order_header.receipt_to is '領収書宛名';

comment on column order_header.receipt_detail is '領収書但し書き';

comment on column order_header.bill_price is '請求額';

comment on column order_header.bill_no is '請求番号';

comment on column order_header.bill_print_count is '請求書発行回数';

comment on column order_header.authority_result_kbn is 'オーソリ結果';

comment on column order_header.authority_no is 'オーソリ識別番号';

comment on column order_header.card_password is 'カードパスワード';

comment on column order_header.authority_approval_no is 'オーソリ承認番号';

comment on column order_header.authority_date is 'オーソリ処理日';

comment on column order_header.authority_price is 'オーソリ金額';

comment on column order_header.authority_cancel_approval_no is 'オーソリキャンセル承認番号';

comment on column order_header.authority_cancel_date is 'オーソリキャンセル処理日';

comment on column order_header.credit_payment_no is 'クレジット決済承認番号';

comment on column order_header.credit_payment_date is 'クレジット決済処理日';

comment on column order_header.credit_payment_price is 'クレジット決済金額';

comment on column order_header.credit_cancel_payment_no is 'クレジット決済キャンセル承認番号';

comment on column order_header.credit_cancel_payment_date is 'クレジット決済キャンセル処理日';

comment on column order_header.credit_result_kbn is 'クレジット処理結果';

comment on column order_header.card_brand is 'カードブランド';

comment on column order_header.credit_card_kanri_no is 'クレジットカードお預かり管理番号';

comment on column order_header.credit_card_kanri_detail_no is 'クレジットカードお預かり管理明細番号';

comment on column order_header.credit_card_no is 'クレジットカード番号';

comment on column order_header.credit_card_meigi is 'クレジットカード名義人';

comment on column order_header.credit_card_valid_year is 'クレジットカード有効期限年';

comment on column order_header.credit_card_valid_month is 'クレジットカード有効期限月';

comment on column order_header.credit_card_pay_count is 'クレジットカード支払回数';

comment on column order_header.payment_bar_code is '払込票バーコード番号';

comment on column order_header.amzn_charge_permission_id is 'AmazonPay注文ID';

comment on column order_header.amzn_charge_id is 'AmazonPay取引ID';

comment on column order_header.amzn_charge_status is 'AmazonPay取引ステータス';

comment on column order_header.amzn_authorization_datetime is 'AmazonPayオーソリ処理日時';

comment on column order_header.amzn_capture_initiated_datetime is 'AmazonPay売上請求処理日時';

comment on column order_header.amzn_captured_datetime is 'AmazonPay売上請求確定日時';

comment on column order_header.amzn_canceled_datetime is 'AmazonPayキャンセル処理日時';

comment on column order_header.order_user_code is '受注担当ユーザコード';

comment on column order_header.order_user is '受注担当ユーザ名';

comment on column order_header.change_user_code is '入力担当ユーザコード';

comment on column order_header.change_user is '入力担当ユーザ名';

comment on column order_header.demand_kbn is '督促区分';

comment on column order_header.demand1_ref_date is '督促１抽出基準日';

comment on column order_header.demand1_date is '督促１処理日';

comment on column order_header.demand1_limit_date is '督促１支払期限';

comment on column order_header.demand1_amount is '督促１金額';

comment on column order_header.demand1_bar_code is '督促１払込票バーコード番号';

comment on column order_header.demand2_ref_date is '督促２抽出基準日';

comment on column order_header.demand2_date is '督促２処理日';

comment on column order_header.demand2_limit_date is '督促２支払期限';

comment on column order_header.demand2_amount is '督促２金額';

comment on column order_header.demand2_bar_code is '督促２払込票バーコード番号';

comment on column order_header.demand3_ref_date is '督促３抽出基準日';

comment on column order_header.demand3_date is '督促３処理日';

comment on column order_header.demand3_limit_date is '督促３支払期限';

comment on column order_header.demand3_amount is '督促３金額';

comment on column order_header.demand3_bar_code is '督促３払込票バーコード番号';

comment on column order_header.kashidaore_date is '貸倒処理日';

comment on column order_header.demand_exclude_reason_kbn is '督促除外理由区分';

comment on column order_header.demand_exclude_start_date is '督促除外開始日';

comment on column order_header.demand_exclude_end_date is '督促除外終了日';

comment on column order_header.bill_sei_kj is '請求先＿姓（漢字）';

comment on column order_header.bill_mei_kj is '請求先＿名（漢字）';

comment on column order_header.bill_sei_kn is '請求先＿姓（かな）';

comment on column order_header.bill_mei_kn is '請求先＿名（かな）';

comment on column order_header.bill_tel_no is '請求先＿電話番号';

comment on column order_header.bill_zipcd is '請求先＿郵便番号';

comment on column order_header.bill_addr1 is '請求先＿住所１';

comment on column order_header.bill_addr2 is '請求先＿住所２';

comment on column order_header.bill_addr3 is '請求先＿住所３';

comment on column order_header.bill_addr4 is '請求先＿住所４';

comment on column order_header.bill_corporation_post_name is '請求先＿会社部署役職名';

comment on column order_header.nohinsyo_uketsuke_tanto is '納品書受付担当者';

comment on column order_header.grant_plan_point_prod is '付与予定ポイント数（商品）';

comment on column order_header.grant_plan_point_other is '付与予定ポイント数（その他）';

comment on column order_header.grant_plan_point_total is '付与予定ポイント数（合計）';

comment on column order_header.grant_point_prod is '付与ポイント数（商品）';

comment on column order_header.grant_point_other is '付与ポイント数（その他）';

comment on column order_header.grant_point_total is '付与ポイント数（合計）';

comment on column order_header.reduction_plan_point_total is '利用予定ポイント数（合計）';

comment on column order_header.reduction_point_total is '利用ポイント数（合計）';

comment on column order_header.subtotal_before_campaign is 'キャンペーン適用前商品小計額';

comment on column order_header.subtotal_after_campaign is 'キャンペーン適用後商品小計額';

comment on column order_header.total_before_campaign is 'キャンペーン適用前金額';

comment on column order_header.orm_rowid is 'データ行ID';

comment on column order_header.created_user is '作成ユーザ';

comment on column order_header.created_datetime is '作成日時';

comment on column order_header.updated_user is '更新ユーザ';

comment on column order_header.updated_datetime is '更新日時';

create unique index order_header_ix0
on order_header
(orm_rowid)
tablespace ts_order_i01;

create index order_header_ix1
on order_header
(email)
tablespace ts_order_i01;

create unique index order_header_ix2
on order_header
(order_no, payment_status, order_status)
tablespace ts_order_i01;

create index order_header_ix3
on order_header
(order_datetime)
tablespace ts_order_i01;

create index order_header_ix4
on order_header
(customer_code)
tablespace ts_order_i01;

create index order_header_ix5
on order_header
(payment_method_no)
tablespace ts_order_i01;

create index order_header_ix6
on order_header
((last_name || coalesce(first_name,'')), (last_name_kana || coalesce(first_name_kana,'')))
tablespace ts_order_i01;

create index order_header_ix7
on order_header
(order_status, data_transport_status)
tablespace ts_order_i01;

create index order_header_ix8
on order_header
(replace(phone_number,'-',null))
tablespace ts_order_i01;

create index order_header_ix9
on order_header
(date_trunc('day', order_datetime))
tablespace ts_order_i01;

create index order_header_ix11
on order_header
(order_create_error_code)
tablespace ts_order_i01;

create index order_header_ix12
on order_header
(payment_order_id)
tablespace ts_order_i01;

create index order_header_ix13
on order_header
(neo_customer_no)
tablespace ts_order_i01;

create index order_header_ix14
on order_header
(external_order_no)
tablespace ts_order_i01;

create index order_header_ix16
on order_header
(last_name)
tablespace ts_order_i01;

create index order_header_ix17
on order_header
(first_name)
tablespace ts_order_i01;

create index order_header_ix18
on order_header
(last_name_kana)
tablespace ts_order_i01;

create index order_header_ix19
on order_header
(first_name_kana)
tablespace ts_order_i01;

create index order_header_ix20
on order_header
(address1)
tablespace ts_order_i01;

create index order_header_ix21
on order_header
(address2)
tablespace ts_order_i01;

create index order_header_ix22
on order_header
(address3)
tablespace ts_order_i01;

create index order_header_ix23
on order_header
(address4)
tablespace ts_order_i01;

create index order_header_ix24
on order_header
(phone_number)
tablespace ts_order_i01;

create index order_header_ix25
on order_header
(ec_promotion_id)
tablespace ts_order_i01;

create index order_header_ix26
on order_header
(ec_campaign_id)
tablespace ts_order_i01;

create index order_header_ix27
on order_header
(order_update_datetime)
tablespace ts_order_i01;

create index order_header_ix28
on order_header
(bill_sei_kj)
tablespace ts_order_i01;

create index order_header_ix29
on order_header
(bill_mei_kj)
tablespace ts_order_i01;

create index order_header_ix30
on order_header
(bill_sei_kn)
tablespace ts_order_i01;

create index order_header_ix31
on order_header
(bill_mei_kn)
tablespace ts_order_i01;

create index order_header_ix32
on order_header
(bill_tel_no)
tablespace ts_order_i01;

create index order_header_ix33
on order_header
(bill_zipcd)
tablespace ts_order_i01;

create index order_header_ix34
on order_header
(shop_code)
tablespace ts_order_i01;

create index order_header_ix35
on order_header
(ext_payment_method_type)
tablespace ts_order_i01;

create index order_header_ix36
on order_header
(order_datetime, shop_code, ext_payment_method_type)
tablespace ts_order_i01;

create index order_header_ix37
on order_header
(ext_order_status)
tablespace ts_order_i01;

create index order_header_ix38
on order_header
(bill_no)
tablespace ts_order_i01;
