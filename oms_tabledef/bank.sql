create table bank
(
    shop_code                      varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    bank_code                      varchar(4) not null,
    bank_branch_code               varchar(3) not null,
    account_no                     varchar(7) not null,
    bank_name                      varchar(40),
    bank_kana                      varchar(40),
    bank_branch_name               varchar(40),
    bank_branch_name_kana          varchar(40),
    account_type                   numeric(1,0),
    account_name                   varchar(40),
    swift_code                     varchar(11),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint bank_pk primary key (shop_code, payment_method_no, bank_code, bank_branch_code, account_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table bank is '金融機関';

comment on column bank.shop_code is 'ショップコード';

comment on column bank.payment_method_no is '支払方法番号';

comment on column bank.bank_code is '金融機関コード';

comment on column bank.bank_branch_code is '金融機関支店コード';

comment on column bank.account_no is '口座番号';

comment on column bank.bank_name is '金融機関名称';

comment on column bank.bank_kana is '金融機関名カナ';

comment on column bank.bank_branch_name is '金融機関支店名称';

comment on column bank.bank_branch_name_kana is '金融機関支店名カナ';

comment on column bank.account_type is '口座種類';

comment on column bank.account_name is '口座名義';

comment on column bank.swift_code is 'SWIFTコード';

comment on column bank.orm_rowid is 'データ行ID';

comment on column bank.created_user is '作成ユーザ';

comment on column bank.created_datetime is '作成日時';

comment on column bank.updated_user is '更新ユーザ';

comment on column bank.updated_datetime is '更新日時';

create unique index bank_ix0
on bank
(orm_rowid)
tablespace ts_shop_i01;
