create table broadcast_mailqueue_detail
(
    mail_queue_id                  numeric(38,0) not null,
    customer_code                  varchar(16) not null,
    to_address                     varchar(256) not null,
    cc_address                     varchar(1000),
    bcc_address                    varchar(1000),
    mail_sent_datetime             timestamp(0),
    mail_send_status               numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint broadcast_mailqueue_detail_pk primary key (mail_queue_id, customer_code, to_address) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table broadcast_mailqueue_detail is '同報配信メールキュー明細';

comment on column broadcast_mailqueue_detail.mail_queue_id is 'メールキューID';

comment on column broadcast_mailqueue_detail.customer_code is '顧客コード';

comment on column broadcast_mailqueue_detail.to_address is 'TOアドレス';

comment on column broadcast_mailqueue_detail.cc_address is 'CCアドレス';

comment on column broadcast_mailqueue_detail.bcc_address is 'BCCアドレス';

comment on column broadcast_mailqueue_detail.mail_sent_datetime is 'メール送信日時';

comment on column broadcast_mailqueue_detail.mail_send_status is 'メール送信ステータス';

comment on column broadcast_mailqueue_detail.orm_rowid is 'データ行ID';

comment on column broadcast_mailqueue_detail.created_user is '作成ユーザ';

comment on column broadcast_mailqueue_detail.created_datetime is '作成日時';

comment on column broadcast_mailqueue_detail.updated_user is '更新ユーザ';

comment on column broadcast_mailqueue_detail.updated_datetime is '更新日時';

create unique index broadcast_mailqueue_detail_ix0
on broadcast_mailqueue_detail
(orm_rowid)
tablespace ts_order_i01;
