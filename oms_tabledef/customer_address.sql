create table customer_address
(
    customer_code                  varchar(16) not null,
    address_no                     numeric(8,0) not null,
    address_alias                  varchar(20) not null,
    address_last_name              varchar(20) not null,
    address_first_name             varchar(20),
    address_last_name_kana         varchar(40) not null,
    address_first_name_kana        varchar(40),
    postal_code                    varchar(7) not null,
    prefecture_code                varchar(2) not null,
    address1                       varchar(4) not null,
    address2                       varchar(50) not null,
    address3                       varchar(255) not null,
    address4                       varchar(100),
    phone_number                   varchar(16),
    corporation_post_name          varchar(40),
    address_id                     varchar(20),
    crm_address_id                 varchar(20),
    crm_address_updated_datetime   varchar(24),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_address_pk primary key (customer_code, address_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_address is '顧客アドレス帳';

comment on column customer_address.customer_code is '顧客コード';

comment on column customer_address.address_no is 'アドレス帳番号';

comment on column customer_address.address_alias is 'アドレス呼称';

comment on column customer_address.address_last_name is '宛名：姓';

comment on column customer_address.address_first_name is '宛名：名';

comment on column customer_address.address_last_name_kana is '宛名姓かな';

comment on column customer_address.address_first_name_kana is '宛名名かな';

comment on column customer_address.postal_code is '郵便番号';

comment on column customer_address.prefecture_code is '都道府県コード';

comment on column customer_address.address1 is '住所1';

comment on column customer_address.address2 is '住所2';

comment on column customer_address.address3 is '住所3';

comment on column customer_address.address4 is '住所4';

comment on column customer_address.phone_number is '電話番号';

comment on column customer_address.corporation_post_name is '会社部署名';

comment on column customer_address.address_id is '住所ID';

comment on column customer_address.crm_address_id is 'CRMアドレスID';

comment on column customer_address.crm_address_updated_datetime is 'CRMアドレス更新日時';

comment on column customer_address.orm_rowid is 'データ行ID';

comment on column customer_address.created_user is '作成ユーザ';

comment on column customer_address.created_datetime is '作成日時';

comment on column customer_address.updated_user is '更新ユーザ';

comment on column customer_address.updated_datetime is '更新日時';

create unique index customer_address_ix0
on customer_address
(orm_rowid)
tablespace ts_customer_i01;

create index customer_address_ix1
on customer_address
(replace(phone_number,'-',null))
tablespace ts_customer_i01;

create index customer_address_ix2
on customer_address
(address_last_name)
tablespace ts_customer_i01;

create index customer_address_ix3
on customer_address
(address_first_name)
tablespace ts_customer_i01;

create index customer_address_ix4
on customer_address
(address_last_name_kana)
tablespace ts_customer_i01;

create index customer_address_ix5
on customer_address
(address_first_name_kana)
tablespace ts_customer_i01;

create index customer_address_ix6
on customer_address
(address1)
tablespace ts_customer_i01;

create index customer_address_ix7
on customer_address
(address2)
tablespace ts_customer_i01;

create index customer_address_ix8
on customer_address
(address3)
tablespace ts_customer_i01;

create index customer_address_ix9
on customer_address
(phone_number)
tablespace ts_customer_i01;

create index customer_address_ix10
on customer_address
(address_id)
tablespace ts_customer_i01;

create index customer_address_ix11
on customer_address
(crm_address_id)
tablespace ts_customer_i01;
