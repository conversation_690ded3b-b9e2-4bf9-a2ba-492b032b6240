create table review_summary
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    review_score                   numeric(3,0) not null,
    review_count                   numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint review_summary_pk primary key (shop_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table review_summary is 'レビュー点数集計';

comment on column review_summary.shop_code is 'ショップコード';

comment on column review_summary.commodity_code is '商品コード';

comment on column review_summary.review_score is 'レビュー点数';

comment on column review_summary.review_count is 'レビュー件数';

comment on column review_summary.orm_rowid is 'データ行ID';

comment on column review_summary.created_user is '作成ユーザ';

comment on column review_summary.created_datetime is '作成日時';

comment on column review_summary.updated_user is '更新ユーザ';

comment on column review_summary.updated_datetime is '更新日時';

create unique index review_summary_ix0
on review_summary
(orm_rowid)
tablespace ts_commodity_i01;
