create table sales_record
(
    earnings_jisseki_no            numeric(15,0) not null,
    order_henpin_kbn               varchar(1) not null,
    sales_detail_kbn               varchar(2) not null,
    shipping_no                    varchar(16),
    shipping_detail_no             numeric(16,0),
    order_no                       varchar(16),
    order_detail_no                numeric(16,0),
    shipping_date                  timestamp(0) not null,
    sales_recording_date           timestamp(0) not null,
    marketing_channel              varchar(2) not null,
    ext_payment_method_type        varchar(2),
    shop_code                      varchar(16),
    main_product_no                varchar(24),
    product_no                     varchar(24),
    period_flg                     varchar(2),
    baitai_code                    varchar(10),
    commodity_code                 varchar(16),
    hinban_code                    varchar(24),
    commodity_name                 varchar(100),
    commodity_kind                 varchar(2),
    commodity_group                varchar(5),
    commodity_series               varchar(5),
    commodity_segment              varchar(5),
    business_segment               varchar(5),
    parent_commodity_code          varchar(16),
    commodity_amount               numeric(8,0),
    campaign_instructions_code     varchar(16),
    coupon_management_code         varchar(16),
    incurred_price                 numeric(8,0) not null,
    tax_group_code                 varchar(8),
    tax_no                         numeric(3,0),
    tax_rate                       numeric(3,0),
    commodity_tax_type             numeric(1,0),
    business_partner_code          varchar(8),
    sales_bumon_cd                 varchar(7),
    channel_cd                     varchar(5),
    sales_link_status              numeric(1,0) not null,
    sales_link_datetime            timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint sales_record_pk primary key (earnings_jisseki_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table sales_record is '売上実績';

comment on column sales_record.earnings_jisseki_no is '売上実績連番';

comment on column sales_record.order_henpin_kbn is '受注返品区分';

comment on column sales_record.sales_detail_kbn is '売上実績明細区分';

comment on column sales_record.shipping_no is '出荷番号';

comment on column sales_record.shipping_detail_no is '出荷明細番号';

comment on column sales_record.order_no is '受注番号';

comment on column sales_record.order_detail_no is '受注明細番号';

comment on column sales_record.shipping_date is '伝票日付';

comment on column sales_record.sales_recording_date is '売上計上日';

comment on column sales_record.marketing_channel is '販売経路';

comment on column sales_record.ext_payment_method_type is '支払方法区分（拡張）';

comment on column sales_record.shop_code is 'ショップコード';

comment on column sales_record.main_product_no is 'オリジナル商品No';

comment on column sales_record.product_no is '商品No';

comment on column sales_record.period_flg is '定期便フラグ';

comment on column sales_record.baitai_code is '媒体コード';

comment on column sales_record.commodity_code is '商品コード';

comment on column sales_record.hinban_code is '品番コード';

comment on column sales_record.commodity_name is '商品名称';

comment on column sales_record.commodity_kind is '商品種別';

comment on column sales_record.commodity_group is '商品分類';

comment on column sales_record.commodity_series is '商品シリーズ';

comment on column sales_record.commodity_segment is '商品セグメント';

comment on column sales_record.business_segment is '事業セグメント';

comment on column sales_record.parent_commodity_code is '親商品コード';

comment on column sales_record.commodity_amount is '商品数';

comment on column sales_record.campaign_instructions_code is 'キャンペーン設定コード';

comment on column sales_record.coupon_management_code is 'クーポン管理コード';

comment on column sales_record.incurred_price is '発生金額';

comment on column sales_record.tax_group_code is '消費税グループコード';

comment on column sales_record.tax_no is '消費税番号';

comment on column sales_record.tax_rate is '消費税率';

comment on column sales_record.commodity_tax_type is '商品消費税区分';

comment on column sales_record.business_partner_code is '取引先コード';

comment on column sales_record.sales_bumon_cd is '売上部門コード';

comment on column sales_record.channel_cd is 'チャネルコード';

comment on column sales_record.sales_link_status is '売上連携完了フラグ';

comment on column sales_record.sales_link_datetime is '売上連携日時';

comment on column sales_record.orm_rowid is 'データ行ID';

comment on column sales_record.created_user is '作成ユーザ';

comment on column sales_record.created_datetime is '作成日時';

comment on column sales_record.updated_user is '更新ユーザ';

comment on column sales_record.updated_datetime is '更新日時';

create unique index sales_record_ix0
on sales_record
(orm_rowid)
tablespace ts_order_i01;

create index sales_record_ix1
on sales_record
(shipping_date, sales_detail_kbn, channel_cd)
tablespace ts_order_i01;
