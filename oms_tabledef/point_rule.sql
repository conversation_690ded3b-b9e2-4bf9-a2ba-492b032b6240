create table point_rule
(
    point_rule_no                  numeric(1,0) not null,
    point_function_enabled_flg     numeric(1,0) not null,
    point_period                   numeric(3,0) not null,
    point_rate                     numeric(3,0) not null,
    point_invest_purchase_price    numeric(8,0) not null,
    bonus_point_term_rate          numeric(3,0) not null,
    bonus_point_start_date         timestamp(0),
    bonus_point_end_date           timestamp(0),
    bonus_point_date               numeric(2,0),
    customer_register_point        numeric(8,0) not null,
    first_purchase_invest_point    numeric(8,0) not null,
    review_contributed_point       numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint point_rule_pk primary key (point_rule_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table point_rule is 'ポイントルール';

comment on column point_rule.point_rule_no is 'ポイントルール番号';

comment on column point_rule.point_function_enabled_flg is 'ポイント機能使用フラグ';

comment on column point_rule.point_period is 'ポイント利用期限(xヶ月)';

comment on column point_rule.point_rate is 'ポイント付与率';

comment on column point_rule.point_invest_purchase_price is 'ポイント付与最低購入金額';

comment on column point_rule.bonus_point_term_rate is 'ボーナスポイント期間付与率';

comment on column point_rule.bonus_point_start_date is 'ボーナスポイント期間開始日';

comment on column point_rule.bonus_point_end_date is 'ボーナスポイント期間終了日';

comment on column point_rule.bonus_point_date is 'ボーナスポイント日';

comment on column point_rule.customer_register_point is '会員登録時ポイント';

comment on column point_rule.first_purchase_invest_point is '初回購入時付与ポイント';

comment on column point_rule.review_contributed_point is 'レビュー投稿時ポイント';

comment on column point_rule.orm_rowid is 'データ行ID';

comment on column point_rule.created_user is '作成ユーザ';

comment on column point_rule.created_datetime is '作成日時';

comment on column point_rule.updated_user is '更新ユーザ';

comment on column point_rule.updated_datetime is '更新日時';

create unique index point_rule_ix0
on point_rule
(orm_rowid)
tablespace ts_shop_i01;
