create table category_price_range
(
    category_code                  varchar(16) not null,
    category_price_threshold       numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint category_price_range_ix0 unique (orm_rowid) using index
        tablespace ts_commodity_i01,
    constraint category_price_range_pk primary key (category_code, category_price_threshold) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table category_price_range is 'カテゴリ価格帯';

comment on column category_price_range.category_code is 'カテゴリコード';

comment on column category_price_range.category_price_threshold is 'カテゴリ価格閾値';

comment on column category_price_range.orm_rowid is 'データ行ID';

comment on column category_price_range.created_user is '作成ユーザ';

comment on column category_price_range.created_datetime is '作成日時';

comment on column category_price_range.updated_user is '更新ユーザ';

comment on column category_price_range.updated_datetime is '更新日時';

