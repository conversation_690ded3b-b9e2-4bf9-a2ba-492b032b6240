create table login_automation
(
    pin                            varchar(128) not null,
    customer_code                  varchar(16) not null,
    deadline_datetime              timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint login_automation_pk primary key (pin) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table login_automation is '自動ログイン管理';

comment on column login_automation.pin is '自動ログインキー';

comment on column login_automation.customer_code is '顧客コード';

comment on column login_automation.deadline_datetime is '自動ログイン期限';

comment on column login_automation.orm_rowid is 'データ行ID';

comment on column login_automation.created_user is '作成ユーザ';

comment on column login_automation.created_datetime is '作成日時';

comment on column login_automation.updated_user is '更新ユーザ';

comment on column login_automation.updated_datetime is '更新日時';

create unique index login_automation_ix0
on login_automation
(orm_rowid)
tablespace ts_customer_i01;
