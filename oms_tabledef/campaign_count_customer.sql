create table campaign_count_customer
(
    campaign_instructions_code     varchar(16) not null,
    customer_code                  varchar(16) not null,
    campaign_used_count            numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_count_customer_pk primary key (campaign_instructions_code, customer_code) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table campaign_count_customer is 'キャンペーン顧客適用件数';

comment on column campaign_count_customer.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_count_customer.customer_code is '顧客コード';

comment on column campaign_count_customer.campaign_used_count is 'キャンペーン累計利用回数';

comment on column campaign_count_customer.orm_rowid is 'データ行ID';

comment on column campaign_count_customer.created_user is '作成ユーザ';

comment on column campaign_count_customer.created_datetime is '作成日時';

comment on column campaign_count_customer.updated_user is '更新ユーザ';

comment on column campaign_count_customer.updated_datetime is '更新日時';

create unique index campaign_count_customer_ix0
on campaign_count_customer
(orm_rowid)
tablespace ts_order_i01;
