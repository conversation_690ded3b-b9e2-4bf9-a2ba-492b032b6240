create table work_bill_print_execute
(
    batch_link_no                  varchar(10) not null,
    bill_print_id                  varchar(10) not null,
    denpatu_request_status         varchar(1),
    report_id                      varchar(28),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_bill_print_execute_pk primary key (batch_link_no, bill_print_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table work_bill_print_execute is '請求書再発行指示ワーク';

comment on column work_bill_print_execute.batch_link_no is 'バッチ連携番号';

comment on column work_bill_print_execute.bill_print_id is '再発行ID';

comment on column work_bill_print_execute.denpatu_request_status is '印刷指示ステータス';

comment on column work_bill_print_execute.report_id is 'レポートID';

comment on column work_bill_print_execute.orm_rowid is 'データ行ID';

comment on column work_bill_print_execute.created_user is '作成ユーザ';

comment on column work_bill_print_execute.created_datetime is '作成日時';

comment on column work_bill_print_execute.updated_user is '更新ユーザ';

comment on column work_bill_print_execute.updated_datetime is '更新日時';

