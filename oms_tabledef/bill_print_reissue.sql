create table bill_print_reissue
(
    bill_print_id                  varchar(10) not null,
    order_no                       varchar(16) not null,
    request_status                 varchar(1) not null,
    bill_print_reason_kbn          varchar(2) not null,
    request_date                   timestamp(0) not null,
    request_user_code              numeric(38,0),
    bill_print_date                timestamp(0),
    bill_print_user_code           numeric(38,0),
    bill_no                        varchar(17),
    bill_bar_code                  varchar(44),
    bill_print_count               numeric(3,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint bill_print_reissue_pl primary key (bill_print_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table bill_print_reissue is '請求書再発行';

comment on column bill_print_reissue.bill_print_id is '再発行ID';

comment on column bill_print_reissue.order_no is '受注番号';

comment on column bill_print_reissue.request_status is '依頼ステータス';

comment on column bill_print_reissue.bill_print_reason_kbn is '再発行理由';

comment on column bill_print_reissue.request_date is '依頼日';

comment on column bill_print_reissue.request_user_code is '依頼担当者ユーザコード';

comment on column bill_print_reissue.bill_print_date is '再発行日';

comment on column bill_print_reissue.bill_print_user_code is '再発行担当ユーザコード';

comment on column bill_print_reissue.bill_no is '請求番号';

comment on column bill_print_reissue.bill_bar_code is '払込票バーコード番号';

comment on column bill_print_reissue.bill_print_count is '請求書発行回数';

comment on column bill_print_reissue.orm_rowid is 'データ行ID';

comment on column bill_print_reissue.created_user is '作成ユーザ';

comment on column bill_print_reissue.created_datetime is '作成日時';

comment on column bill_print_reissue.updated_user is '更新ユーザ';

comment on column bill_print_reissue.updated_datetime is '更新日時';

create unique index bill_print_reissue_ix0
on bill_print_reissue
(orm_rowid)
tablespace ts_order_i01;

create index bill_print_reissue_ix1
on bill_print_reissue
(request_status)
tablespace ts_order_i01;

create index bill_print_reissue_ix2
on bill_print_reissue
(request_date)
tablespace ts_order_i01;

create index bill_print_reissue_ix3
on bill_print_reissue
(order_no)
tablespace ts_order_i01;
