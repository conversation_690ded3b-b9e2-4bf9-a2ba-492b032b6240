create table work_shipping_detail
(
    accept_no                      varchar(22) not null,
    seq                            numeric(5,0) not null,
    prod_no                        numeric(10,0) not null,
    qty                            numeric(7,0),
    chit_print_date                varchar(8),
    yamato_bar_code                varchar(14),
    gyosha_flg                     varchar(1),
    invoice_branch_number          varchar(1),
    bumon_kbn                      varchar(1),
    prod_price                     numeric(6,0),
    order_type                     varchar(1),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_shipping_detail_pk primary key (accept_no, seq, prod_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_shipping_detail is '出荷明細ワーク';

comment on column work_shipping_detail.accept_no is '基幹出荷指示番号';

comment on column work_shipping_detail.seq is '連番';

comment on column work_shipping_detail.prod_no is '商品番号';

comment on column work_shipping_detail.qty is '数量';

comment on column work_shipping_detail.chit_print_date is '検品日';

comment on column work_shipping_detail.yamato_bar_code is '問い合わせ番号';

comment on column work_shipping_detail.gyosha_flg is '配送業者コード';

comment on column work_shipping_detail.invoice_branch_number is '未使用_invoice_branch_number';

comment on column work_shipping_detail.bumon_kbn is '未使用_bumon_kbm';

comment on column work_shipping_detail.prod_price is '未使用_prod_price';

comment on column work_shipping_detail.order_type is '直営種別';

comment on column work_shipping_detail.orm_rowid is 'データ行ID';

comment on column work_shipping_detail.created_user is '作成ユーザ';

comment on column work_shipping_detail.created_datetime is '作成日時';

comment on column work_shipping_detail.updated_user is '更新ユーザ';

comment on column work_shipping_detail.updated_datetime is '更新日時';

create unique index work_shipping_detail_ix0
on work_shipping_detail
(orm_rowid)
tablespace ts_order_i01;
