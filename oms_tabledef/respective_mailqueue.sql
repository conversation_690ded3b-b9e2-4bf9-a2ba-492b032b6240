create table respective_mailqueue
(
    mail_queue_id                  numeric(38,0) not null,
    mail_type                      varchar(2) not null,
    mail_content_type              numeric(1,0) not null,
    mail_subject                   varchar(100) not null,
    mail_sender_name               varchar(50),
    from_address                   varchar(256) not null,
    to_address                     varchar(256),
    cc_address                     varchar(1000),
    bcc_address                    varchar(1000),
    mail_text                      text,
    mail_send_status               numeric(1,0) not null,
    mail_sent_datetime             timestamp(0),
    crm_customer_id                varchar(20),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint respective_mailqueue_pk primary key (mail_queue_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table respective_mailqueue is '個別配信メールキュー';

comment on column respective_mailqueue.mail_queue_id is 'メールキューID';

comment on column respective_mailqueue.mail_type is 'メールタイプ';

comment on column respective_mailqueue.mail_content_type is 'メールコンテンツタイプ';

comment on column respective_mailqueue.mail_subject is 'メール件名';

comment on column respective_mailqueue.mail_sender_name is '差出人名';

comment on column respective_mailqueue.from_address is 'FROMアドレス';

comment on column respective_mailqueue.to_address is 'TOアドレス';

comment on column respective_mailqueue.cc_address is 'CCアドレス';

comment on column respective_mailqueue.bcc_address is 'BCCアドレス';

comment on column respective_mailqueue.mail_text is 'メール本文';

comment on column respective_mailqueue.mail_send_status is 'メール送信ステータス';

comment on column respective_mailqueue.mail_sent_datetime is 'メール送信日時';

comment on column respective_mailqueue.crm_customer_id is 'CRM顧客ID';

comment on column respective_mailqueue.orm_rowid is 'データ行ID';

comment on column respective_mailqueue.created_user is '作成ユーザ';

comment on column respective_mailqueue.created_datetime is '作成日時';

comment on column respective_mailqueue.updated_user is '更新ユーザ';

comment on column respective_mailqueue.updated_datetime is '更新日時';

create unique index respective_mailqueue_ix0
on respective_mailqueue
(orm_rowid)
tablespace ts_order_i01;

create index respective_mailqueue_ix1
on respective_mailqueue
(mail_type, mail_send_status)
tablespace ts_order_i01;
