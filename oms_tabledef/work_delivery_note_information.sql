create table work_delivery_note_information
(
    slip_ino                       varchar(14) not null,
    msg_seq                        numeric(3,0) not null,
    msg_text                       varchar(1000),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_delivery_note_information_pk primary key (slip_ino, msg_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_delivery_note_information is '納品書お知らせ欄ワーク';

comment on column work_delivery_note_information.slip_ino is '基幹出荷指示番号';

comment on column work_delivery_note_information.msg_seq is 'シーケンス';

comment on column work_delivery_note_information.msg_text is 'お知らせ内容';

comment on column work_delivery_note_information.orm_rowid is 'データ行ID';

comment on column work_delivery_note_information.created_user is '作成ユーザ';

comment on column work_delivery_note_information.created_datetime is '作成日時';

comment on column work_delivery_note_information.updated_user is '更新ユーザ';

comment on column work_delivery_note_information.updated_datetime is '更新日時';

create unique index work_delivery_note_information_ix0
on work_delivery_note_information
(orm_rowid)
tablespace ts_order_i01;
