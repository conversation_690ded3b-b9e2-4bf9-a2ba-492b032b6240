create table yupack_zip
(
    yupack_postal_code             varchar(7) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint yupack_zip_pk primary key (yupack_postal_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table yupack_zip is 'ゆうパック対象郵便番号マスタ';

comment on column yupack_zip.yupack_postal_code is 'ゆうパック対象郵便番号';

comment on column yupack_zip.orm_rowid is 'データ行ID';

comment on column yupack_zip.created_user is '作成ユーザ';

comment on column yupack_zip.created_datetime is '作成日時';

comment on column yupack_zip.updated_user is '更新ユーザ';

comment on column yupack_zip.updated_datetime is '更新日時';

create unique index yupack_zip_ix0
on yupack_zip
(orm_rowid)
tablespace ts_shop_i01;
