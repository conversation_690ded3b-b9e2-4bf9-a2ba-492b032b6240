create table standard_commodity
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    sku_code                       varchar(24) not null,
    standard_no                    numeric(2,0) not null,
    standard_code                  varchar(16) not null,
    standard_detail_code           varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint standard_commodity_pk primary key (shop_code, commodity_code, sku_code, standard_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table standard_commodity is '規格商品関連付け';

comment on column standard_commodity.shop_code is 'ショップコード';

comment on column standard_commodity.commodity_code is '商品コード';

comment on column standard_commodity.sku_code is 'SKUコード';

comment on column standard_commodity.standard_no is '規格番号';

comment on column standard_commodity.standard_code is '規格コード';

comment on column standard_commodity.standard_detail_code is '規格詳細コード';

comment on column standard_commodity.orm_rowid is 'データ行ID';

comment on column standard_commodity.created_user is '作成ユーザ';

comment on column standard_commodity.created_datetime is '作成日時';

comment on column standard_commodity.updated_user is '更新ユーザ';

comment on column standard_commodity.updated_datetime is '更新日時';

create unique index standard_commodity_ix0
on standard_commodity
(orm_rowid)
tablespace ts_commodity_i01;
