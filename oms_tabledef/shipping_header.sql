create table shipping_header
(
    shipping_no                    varchar(16) not null,
    order_no                       varchar(16) not null,
    shop_code                      varchar(16) not null,
    customer_code                  varchar(16),
    neo_customer_no                varchar(12),
    address_no                     numeric(8,0) not null,
    address_last_name              varchar(20) not null,
    address_first_name             varchar(20),
    address_last_name_kana         varchar(40) not null,
    address_first_name_kana        varchar(40),
    postal_code                    varchar(7) not null,
    prefecture_code                varchar(2) not null,
    address1                       varchar(4) not null,
    address2                       varchar(50) not null,
    address3                       varchar(255) not null,
    address4                       varchar(100),
    corporation_post_name          varchar(40),
    phone_number                   varchar(16),
    delivery_remark                varchar(500),
    acquired_point                 numeric(9,0),
    delivery_slip_no               varchar(30),
    shipping_charge                numeric(8,0) not null,
    shipping_charge_tax_type       numeric(1,0) not null,
    shipping_charge_tax_group_code varchar(8) not null,
    shipping_charge_tax_no         numeric(3,0) not null,
    shipping_charge_tax_rate       numeric(3,0) not null,
    shipping_charge_tax            numeric(10,2) not null,
    delivery_type_no               numeric(8,0) not null,
    shipping_method                varchar(2) not null,
    delivery_type_name             varchar(40),
    delivery_appointed_date        timestamp(0),
    delivery_appointed_time_start  numeric(2,0),
    delivery_appointed_time_end    numeric(2,0),
    arrival_date                   timestamp(0),
    arrival_time_start             numeric(2,0),
    arrival_time_end               numeric(2,0),
    fixed_sales_status             numeric(1,0) not null,
    shipping_status                numeric(1,0) not null,
    shipping_direct_date           timestamp(0),
    shipping_date                  timestamp(0),
    original_shipping_no           varchar(16),
    return_item_date               timestamp(0),
    return_item_type               numeric(1,0),
    shipping_area                  varchar(2) not null,
    delivery_note_flg              numeric(1,0) not null,
    include_flg                    numeric(1,0) not null,
    delivery_memo                  varchar(20),
    shipping_bill_price            numeric(10,0) not null,
    shipping_dokon_shiji_code      varchar(80),
    o_name_disp_kbn                varchar(1) not null,
    member_stage                   varchar(2) not null,
    possession_point               numeric(10,0),
    sales_recording_date           timestamp(0),
    prod_pack_type                 varchar(2),
    shipping_method_kbn            varchar(2),
    box_code                       varchar(2),
    delivery_note_message          text,
    sagawa_collect_date            timestamp(0),
    delivery_info_no               varchar(8),
    receive_shop_type              varchar(1),
    receive_shop_id                varchar(12),
    receive_shop_name              varchar(30),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_header_pk primary key (shipping_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table shipping_header is '出荷ヘッダ';

comment on column shipping_header.shipping_no is '出荷番号';

comment on column shipping_header.order_no is '受注番号';

comment on column shipping_header.shop_code is 'ショップコード';

comment on column shipping_header.customer_code is '顧客コード';

comment on column shipping_header.neo_customer_no is '顧客番号';

comment on column shipping_header.address_no is 'アドレス帳番号';

comment on column shipping_header.address_last_name is '宛名：姓';

comment on column shipping_header.address_first_name is '宛名：名';

comment on column shipping_header.address_last_name_kana is '宛名姓かな';

comment on column shipping_header.address_first_name_kana is '宛名名かな';

comment on column shipping_header.postal_code is '郵便番号';

comment on column shipping_header.prefecture_code is '都道府県コード';

comment on column shipping_header.address1 is '住所1';

comment on column shipping_header.address2 is '住所2';

comment on column shipping_header.address3 is '住所3';

comment on column shipping_header.address4 is '住所4';

comment on column shipping_header.corporation_post_name is '会社部署名';

comment on column shipping_header.phone_number is '電話番号';

comment on column shipping_header.delivery_remark is '配送先備考';

comment on column shipping_header.acquired_point is '獲得ポイント';

comment on column shipping_header.delivery_slip_no is '宅配便伝票番号';

comment on column shipping_header.shipping_charge is '送料';

comment on column shipping_header.shipping_charge_tax_type is '送料消費税区分';

comment on column shipping_header.shipping_charge_tax_group_code is '送料消費税グループコード';

comment on column shipping_header.shipping_charge_tax_no is '送料消費税番号';

comment on column shipping_header.shipping_charge_tax_rate is '送料消費税率';

comment on column shipping_header.shipping_charge_tax is '送料消費税額';

comment on column shipping_header.delivery_type_no is '配送種別番号';

comment on column shipping_header.shipping_method is '配送方法';

comment on column shipping_header.delivery_type_name is '配送種別名称';

comment on column shipping_header.delivery_appointed_date is '配送指定日';

comment on column shipping_header.delivery_appointed_time_start is '配送指定時間開始';

comment on column shipping_header.delivery_appointed_time_end is '配送指定時間終了';

comment on column shipping_header.arrival_date is '到着予定日';

comment on column shipping_header.arrival_time_start is '到着時間開始';

comment on column shipping_header.arrival_time_end is '到着時間終了';

comment on column shipping_header.fixed_sales_status is '売上確定ステータス';

comment on column shipping_header.shipping_status is '出荷ステータス';

comment on column shipping_header.shipping_direct_date is '出荷指示日';

comment on column shipping_header.shipping_date is '発送日';

comment on column shipping_header.original_shipping_no is '元出荷番号';

comment on column shipping_header.return_item_date is '返品日';

comment on column shipping_header.return_item_type is '返品区分';

comment on column shipping_header.shipping_area is '発送場所';

comment on column shipping_header.delivery_note_flg is '納品書不要フラグ';

comment on column shipping_header.include_flg is '同梱不要フラグ';

comment on column shipping_header.delivery_memo is '配送メモ';

comment on column shipping_header.shipping_bill_price is '発送時請求額';

comment on column shipping_header.shipping_dokon_shiji_code is '発送同梱指示コード';

comment on column shipping_header.o_name_disp_kbn is '依頼主＿名前表示区分';

comment on column shipping_header.member_stage is '会員ステージ';

comment on column shipping_header.possession_point is '保有ポイント数';

comment on column shipping_header.sales_recording_date is '売上計上日';

comment on column shipping_header.prod_pack_type is '包装種類';

comment on column shipping_header.shipping_method_kbn is '配送方法指定区分';

comment on column shipping_header.box_code is 'ボックスコード';

comment on column shipping_header.delivery_note_message is '納品書メッセージ';

comment on column shipping_header.sagawa_collect_date is '佐川集荷日';

comment on column shipping_header.delivery_info_no is '配送情報番号';

comment on column shipping_header.receive_shop_type is '受取店舗区分';

comment on column shipping_header.receive_shop_id is '受取店舗ID';

comment on column shipping_header.receive_shop_name is '受取店舗名称';

comment on column shipping_header.orm_rowid is 'データ行ID';

comment on column shipping_header.created_user is '作成ユーザ';

comment on column shipping_header.created_datetime is '作成日時';

comment on column shipping_header.updated_user is '更新ユーザ';

comment on column shipping_header.updated_datetime is '更新日時';

create unique index shipping_header_ix0
on shipping_header
(orm_rowid)
tablespace ts_order_i01;

create index shipping_header_ix1
on shipping_header
(order_no)
tablespace ts_order_i01;

create index shipping_header_ix2
on shipping_header
(shipping_direct_date)
tablespace ts_order_i01;

create index shipping_header_ix3
on shipping_header
(shipping_date)
tablespace ts_order_i01;

create index shipping_header_ix4
on shipping_header
(delivery_slip_no)
tablespace ts_order_i01;

create unique index shipping_header_ix5
on shipping_header
(shipping_no, order_no, fixed_sales_status, shipping_status, return_item_type)
tablespace ts_order_i01;

create unique index shipping_header_ix6
on shipping_header
(customer_code, address_no, shipping_no)
tablespace ts_order_i01;

create index shipping_header_ix7
on shipping_header
(shipping_status)
tablespace ts_order_i01;

create index shipping_header_ix8
on shipping_header
(neo_customer_no)
tablespace ts_order_i01;

create index shipping_header_ix9
on shipping_header
(address_last_name)
tablespace ts_order_i01;

create index shipping_header_ix10
on shipping_header
(address_first_name)
tablespace ts_order_i01;

create index shipping_header_ix11
on shipping_header
(address_last_name_kana)
tablespace ts_order_i01;

create index shipping_header_ix12
on shipping_header
(address_first_name_kana)
tablespace ts_order_i01;

create index shipping_header_ix13
on shipping_header
(address1)
tablespace ts_order_i01;

create index shipping_header_ix14
on shipping_header
(address2)
tablespace ts_order_i01;

create index shipping_header_ix15
on shipping_header
(address3)
tablespace ts_order_i01;

create index shipping_header_ix16
on shipping_header
(address4)
tablespace ts_order_i01;

create index shipping_header_ix17
on shipping_header
(phone_number)
tablespace ts_order_i01;

create index shipping_header_ix18
on shipping_header
(delivery_appointed_date)
tablespace ts_order_i01;

create index shipping_header_ix19
on shipping_header
(sales_recording_date)
tablespace ts_order_i01;
