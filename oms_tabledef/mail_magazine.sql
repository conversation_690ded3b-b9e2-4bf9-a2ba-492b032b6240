create table mail_magazine
(
    mail_magazine_code             varchar(16) not null,
    mail_magazine_title            varchar(20),
    mail_magazine_description      varchar(300),
    display_flg                    numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint mail_magazine_pk primary key (mail_magazine_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table mail_magazine is 'メールマガジン';

comment on column mail_magazine.mail_magazine_code is 'メールマガジンコード';

comment on column mail_magazine.mail_magazine_title is 'メールマガジンタイトル';

comment on column mail_magazine.mail_magazine_description is 'メールマガジン説明';

comment on column mail_magazine.display_flg is '表示フラグ';

comment on column mail_magazine.orm_rowid is 'データ行ID';

comment on column mail_magazine.created_user is '作成ユーザ';

comment on column mail_magazine.created_datetime is '作成日時';

comment on column mail_magazine.updated_user is '更新ユーザ';

comment on column mail_magazine.updated_datetime is '更新日時';

create unique index mail_magazine_ix0
on mail_magazine
(orm_rowid)
tablespace ts_shop_i01;
