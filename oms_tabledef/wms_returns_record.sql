create table wms_returns_record
(
    wms_henpin_jisseki_seq         numeric(15,0) not null,
    wms_henpin_rireki_no           varchar(10) not null,
    sales_recording_date           timestamp(0) not null,
    sales_recording_flg            numeric(1,0) not null,
    hinban_code                    varchar(24),
    henpin_qt                      numeric(5,0),
    sales_customer_cd              varchar(11) not null,
    sales_bumon_cd                 varchar(7) not null,
    sales_warehouse_cd             varchar(11) not null,
    sales_link_status              numeric(1,0) not null,
    sales_link_datetime            timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint wms_returns_record_pk primary key (wms_henpin_jisseki_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table wms_returns_record is 'WMS返品実績';

comment on column wms_returns_record.wms_henpin_jisseki_seq is 'WMS返品実績連番';

comment on column wms_returns_record.wms_henpin_rireki_no is 'WMS返品履歴番号';

comment on column wms_returns_record.sales_recording_date is '売上計上日';

comment on column wms_returns_record.sales_recording_flg is '売上計上フラグ';

comment on column wms_returns_record.hinban_code is '品番コード';

comment on column wms_returns_record.henpin_qt is '返品数量';

comment on column wms_returns_record.sales_customer_cd is '売上得意先コード';

comment on column wms_returns_record.sales_bumon_cd is '売上部門コード';

comment on column wms_returns_record.sales_warehouse_cd is '売上倉庫コード';

comment on column wms_returns_record.sales_link_status is '売上連携完了フラグ';

comment on column wms_returns_record.sales_link_datetime is '売上連携日時';

comment on column wms_returns_record.orm_rowid is 'データ行ID';

comment on column wms_returns_record.created_user is '作成ユーザ';

comment on column wms_returns_record.created_datetime is '作成日時';

comment on column wms_returns_record.updated_user is '更新ユーザ';

comment on column wms_returns_record.updated_datetime is '更新日時';

create unique index wms_returns_record_ix0
on wms_returns_record
(orm_rowid)
tablespace ts_order_i01;
