create table holiday
(
    holiday_id                     numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    holiday                        timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint holiday_pk primary key (holiday_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table holiday is '休日';

comment on column holiday.holiday_id is '休日ID';

comment on column holiday.shop_code is 'ショップコード';

comment on column holiday.holiday is '休日';

comment on column holiday.orm_rowid is 'データ行ID';

comment on column holiday.created_user is '作成ユーザ';

comment on column holiday.created_datetime is '作成日時';

comment on column holiday.updated_user is '更新ユーザ';

comment on column holiday.updated_datetime is '更新日時';

create unique index holiday_ix0
on holiday
(orm_rowid)
tablespace ts_shop_i01;
