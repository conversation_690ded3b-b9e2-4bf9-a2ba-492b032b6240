create table payment
(
    payment_no                     numeric(10,0) not null,
    order_no                       varchar(16) not null,
    payment_kbn                    varchar(2) not null,
    payment_method_kbn             varchar(2) not null,
    prompt_confirm_kbn             varchar(1) not null,
    payment_price                  numeric(10,0) not null,
    bill_price                     numeric(10,0) not null,
    customer_payment_datetime      timestamp(0),
    payment_prompt_datetime        timestamp(0),
    payment_datetime               timestamp(0),
    payment_confirm_date           timestamp(0),
    payment_memo                   varchar(1000),
    payment_user_code              numeric(38,0),
    bill_no                        varchar(17),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint payment_pk primary key (payment_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table payment is '入金内訳';

comment on column payment.payment_no is '入金内訳番号';

comment on column payment.order_no is '受注番号';

comment on column payment.payment_kbn is '入金区分';

comment on column payment.payment_method_kbn is '入金方法区分';

comment on column payment.prompt_confirm_kbn is '速報確定区分';

comment on column payment.payment_price is '入金額';

comment on column payment.bill_price is '入金前売掛残';

comment on column payment.customer_payment_datetime is 'お客様入金日時';

comment on column payment.payment_prompt_datetime is '入金速報処理日時';

comment on column payment.payment_datetime is '入金処理日時';

comment on column payment.payment_confirm_date is '入金確認日';

comment on column payment.payment_memo is '入金メモ';

comment on column payment.payment_user_code is '入金担当ユーザコード';

comment on column payment.bill_no is '請求番号';

comment on column payment.orm_rowid is 'データ行ID';

comment on column payment.created_user is '作成ユーザ';

comment on column payment.created_datetime is '作成日時';

comment on column payment.updated_user is '更新ユーザ';

comment on column payment.updated_datetime is '更新日時';

create unique index payment_ix0
on payment
(orm_rowid)
tablespace ts_order_i01;

create index payment_ix1
on payment
(order_no)
tablespace ts_order_i01;

create index payment_ix2
on payment
(payment_kbn)
tablespace ts_order_i01;

create index payment_ix3
on payment
(payment_method_kbn)
tablespace ts_order_i01;

create index payment_ix4
on payment
(payment_datetime)
tablespace ts_order_i01;

create index payment_ix5
on payment
(payment_user_code)
tablespace ts_order_i01;
