create table batch_linkage
(
    batch_link_no                  varchar(10) not null,
    batch_link_id                  varchar(10),
    batch_proc_nm                  varchar(90),
    batch_status_kbn               varchar(1),
    request_datetime               timestamp(0),
    request_user_code              numeric(38,0),
    batch_proc_condition           text,
    batch_start_datetime           timestamp(0),
    batch_end_datetime             timestamp(0),
    batch_proc_count               numeric(8,0),
    batch_proc_result              varchar(2),
    batch_error_count              numeric(8,0),
    upload_flne_name               text,
    exists_download_file           varchar(1),
    download_file_name             text,
    download_count                 numeric(3,0),
    last_download_datetime         timestamp(0),
    last_download_user_code        numeric(38,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint batch_linkage_pk primary key (batch_link_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table batch_linkage is 'バッチ連携管理';

comment on column batch_linkage.batch_link_no is 'バッチ連携番号';

comment on column batch_linkage.batch_link_id is 'バッチＩＤ';

comment on column batch_linkage.batch_proc_nm is 'バッチ処理名';

comment on column batch_linkage.batch_status_kbn is '処理状態';

comment on column batch_linkage.request_datetime is '処理要求日時';

comment on column batch_linkage.request_user_code is '処理要求ユーザコード';

comment on column batch_linkage.batch_proc_condition is 'バッチ処理条件';

comment on column batch_linkage.batch_start_datetime is 'バッチ処理開始日時';

comment on column batch_linkage.batch_end_datetime is 'バッチ処理終了日時';

comment on column batch_linkage.batch_proc_count is '処理件数';

comment on column batch_linkage.batch_proc_result is '処理結果';

comment on column batch_linkage.batch_error_count is 'エラー件数';

comment on column batch_linkage.upload_flne_name is 'アップロードファイル名';

comment on column batch_linkage.exists_download_file is 'ダウンロードデータ有無';

comment on column batch_linkage.download_file_name is 'ダウンロードファイル名';

comment on column batch_linkage.download_count is 'ダウンロード回数';

comment on column batch_linkage.last_download_datetime is '最終ダウンロード日時';

comment on column batch_linkage.last_download_user_code is '最終ダウンロードユーザコード';

comment on column batch_linkage.orm_rowid is 'データ行ID';

comment on column batch_linkage.created_user is '作成ユーザ';

comment on column batch_linkage.created_datetime is '作成日時';

comment on column batch_linkage.updated_user is '更新ユーザ';

comment on column batch_linkage.updated_datetime is '更新日時';

create unique index batch_linkage_ix0
on batch_linkage
(orm_rowid)
tablespace ts_shop_i01;
