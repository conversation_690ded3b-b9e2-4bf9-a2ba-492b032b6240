create table combine_customer_list
(
    dokon_shiji_code               varchar(5) not null,
    customer_code                  varchar(16) not null,
    dokon_kind                     varchar(1) not null,
    dokon_shiji_flg                numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_customer_list_pk primary key (dokon_shiji_code, customer_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table combine_customer_list is '同梱顧客リスト';

comment on column combine_customer_list.dokon_shiji_code is '同梱指示コード';

comment on column combine_customer_list.customer_code is '顧客コード';

comment on column combine_customer_list.dokon_kind is '条件分類';

comment on column combine_customer_list.dokon_shiji_flg is '同梱指示フラグ';

comment on column combine_customer_list.orm_rowid is 'データ行ID';

comment on column combine_customer_list.created_user is '作成ユーザ';

comment on column combine_customer_list.created_datetime is '作成日時';

comment on column combine_customer_list.updated_user is '更新ユーザ';

comment on column combine_customer_list.updated_datetime is '更新日時';

create unique index combine_customer_list_ix0
on combine_customer_list
(orm_rowid)
tablespace ts_commodity_i01;
