create table order_mail_history
(
    order_mail_history_id          numeric(38,0) not null,
    order_no                       varchar(16) not null,
    shipping_no                    varchar(16),
    mail_queue_id                  numeric(38,0) not null,
    mail_type                      varchar(2) not null,
    mail_send_status               numeric(1,0) not null,
    mail_sent_datetime             timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_mail_history_pk primary key (order_mail_history_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_mail_history is '受注メール送信履歴';

comment on column order_mail_history.order_mail_history_id is '受注メール送信履歴ID';

comment on column order_mail_history.order_no is '受注番号';

comment on column order_mail_history.shipping_no is '出荷番号';

comment on column order_mail_history.mail_queue_id is 'メールキューID';

comment on column order_mail_history.mail_type is 'メールタイプ';

comment on column order_mail_history.mail_send_status is 'メール送信ステータス';

comment on column order_mail_history.mail_sent_datetime is 'メール送信日時';

comment on column order_mail_history.orm_rowid is 'データ行ID';

comment on column order_mail_history.created_user is '作成ユーザ';

comment on column order_mail_history.created_datetime is '作成日時';

comment on column order_mail_history.updated_user is '更新ユーザ';

comment on column order_mail_history.updated_datetime is '更新日時';

create unique index order_mail_history_ix0
on order_mail_history
(orm_rowid)
tablespace ts_order_i01;

create index order_mail_history_ix1
on order_mail_history
(order_no)
tablespace ts_order_i01;

create index order_mail_history_ix2
on order_mail_history
(shipping_no)
tablespace ts_order_i01;

create index order_mail_history_ix3
on order_mail_history
(mail_queue_id)
tablespace ts_order_i01;
