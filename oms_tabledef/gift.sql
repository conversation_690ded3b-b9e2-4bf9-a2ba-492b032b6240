create table gift
(
    shop_code                      varchar(16) not null,
    gift_code                      varchar(16) not null,
    gift_name                      varchar(40) not null,
    gift_description               varchar(200),
    gift_price                     numeric(8,0) not null,
    display_order                  numeric(8,0) not null,
    gift_tax_type                  numeric(1,0) not null,
    display_flg                    numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint gift_pk primary key (shop_code, gift_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table gift is 'ギフト';

comment on column gift.shop_code is 'ショップコード';

comment on column gift.gift_code is 'ギフトコード';

comment on column gift.gift_name is 'ギフト名称';

comment on column gift.gift_description is 'ギフト説明';

comment on column gift.gift_price is 'ギフト価格';

comment on column gift.display_order is '表示順';

comment on column gift.gift_tax_type is 'ギフト消費税区分';

comment on column gift.display_flg is '表示フラグ';

comment on column gift.orm_rowid is 'データ行ID';

comment on column gift.created_user is '作成ユーザ';

comment on column gift.created_datetime is '作成日時';

comment on column gift.updated_user is '更新ユーザ';

comment on column gift.updated_datetime is '更新日時';

create unique index gift_ix0
on gift
(orm_rowid)
tablespace ts_commodity_i01;

create index gift_ix1
on gift
(gift_name)
tablespace ts_commodity_i01;

create unique index gift_lpk
on gift
(lower(shop_code), lower(gift_code))
tablespace ts_commodity_i01;
