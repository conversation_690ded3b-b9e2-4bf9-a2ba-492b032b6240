create table returns_campaign
(
    henpin_request_no              varchar(10) not null,
    campaign_instructions_code     varchar(16) not null,
    campaign_instructions_name     varchar(50) not null,
    campaign_description           varchar(100),
    campaign_end_date              timestamp(0) not null,
    commodity_code                 varchar(16),
    commodity_name                 varchar(100),
    return_before_after_type       varchar(1),
    select_campaign_instructions_code varchar(16),
    before_campaign_use_amount     numeric(8,0),
    after_campaign_use_amount      numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint returns_campaign_pk primary key (henpin_request_no, campaign_instructions_code) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table returns_campaign is '返品キャンペーン適用';

comment on column returns_campaign.henpin_request_no is '返品依頼番号';

comment on column returns_campaign.campaign_instructions_code is 'キャンペーン設定コード';

comment on column returns_campaign.campaign_instructions_name is 'キャンペーン設定名称';

comment on column returns_campaign.campaign_description is 'キャンペーン内容';

comment on column returns_campaign.campaign_end_date is 'キャンペーン適用終了日';

comment on column returns_campaign.commodity_code is '商品コード';

comment on column returns_campaign.commodity_name is '商品名称';

comment on column returns_campaign.return_before_after_type is '返品前後区分';

comment on column returns_campaign.select_campaign_instructions_code is '選択式キャンペーン設定コード';

comment on column returns_campaign.before_campaign_use_amount is '返品前キャンペーン利用額';

comment on column returns_campaign.after_campaign_use_amount is '返品後キャンペーン利用額';

comment on column returns_campaign.orm_rowid is 'データ行ID';

comment on column returns_campaign.created_user is '作成ユーザ';

comment on column returns_campaign.created_datetime is '作成日時';

comment on column returns_campaign.updated_user is '更新ユーザ';

comment on column returns_campaign.updated_datetime is '更新日時';

create unique index returns_campaign_ix0
on returns_campaign
(orm_rowid)
tablespace ts_order_i01;
