create table shipping_detail_inc_work
(
    shipping_no                    varchar(16) not null,
    shipping_detail_no             numeric(16,0) not null,
    inc_work_composition_no        numeric(2,0) not null,
    shop_code                      varchar(16) not null,
    inc_work_code                  varchar(16) not null,
    inc_work_name                  varchar(50) not null,
    display_order                  numeric(8,0) not null,
    inc_work_detail_no             numeric(8,0) not null,
    inc_work_choices               varchar(30) not null,
    free_text_title                varchar(50),
    free_text_value                varchar(50),
    inc_work_price                 numeric(8,0) not null,
    inc_work_tax_group_code        varchar(8) not null,
    inc_work_tax_no                numeric(3,0) not null,
    inc_work_tax_rate              numeric(3,0) not null,
    inc_work_tax                   numeric(10,2) not null,
    inc_work_tax_type              numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_detail_inc_work_pk primary key (shipping_no, shipping_detail_no, inc_work_composition_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table shipping_detail_inc_work is '出荷明細付帯サービス構成';

comment on column shipping_detail_inc_work.shipping_no is '出荷番号';

comment on column shipping_detail_inc_work.shipping_detail_no is '出荷明細番号';

comment on column shipping_detail_inc_work.inc_work_composition_no is '付帯サービス構成項番';

comment on column shipping_detail_inc_work.shop_code is 'ショップコード';

comment on column shipping_detail_inc_work.inc_work_code is '付帯サービスコード';

comment on column shipping_detail_inc_work.inc_work_name is '付帯サービス名称';

comment on column shipping_detail_inc_work.display_order is '表示順';

comment on column shipping_detail_inc_work.inc_work_detail_no is '付帯サービス詳細番号';

comment on column shipping_detail_inc_work.inc_work_choices is '付帯サービス選択肢名称';

comment on column shipping_detail_inc_work.free_text_title is '自由入力欄タイトル';

comment on column shipping_detail_inc_work.free_text_value is '自由入力欄入力値';

comment on column shipping_detail_inc_work.inc_work_price is '付帯サービス価格';

comment on column shipping_detail_inc_work.inc_work_tax_group_code is '付帯サービス消費税グループコード';

comment on column shipping_detail_inc_work.inc_work_tax_no is '付帯サービス消費税番号';

comment on column shipping_detail_inc_work.inc_work_tax_rate is '付帯サービス消費税率';

comment on column shipping_detail_inc_work.inc_work_tax is '付帯サービス消費税額';

comment on column shipping_detail_inc_work.inc_work_tax_type is '付帯サービス消費税区分';

comment on column shipping_detail_inc_work.orm_rowid is 'データ行ID';

comment on column shipping_detail_inc_work.created_user is '作成ユーザ';

comment on column shipping_detail_inc_work.created_datetime is '作成日時';

comment on column shipping_detail_inc_work.updated_user is '更新ユーザ';

comment on column shipping_detail_inc_work.updated_datetime is '更新日時';

create unique index shipping_detail_inc_work_ix0
on shipping_detail_inc_work
(orm_rowid)
tablespace ts_order_i01;
