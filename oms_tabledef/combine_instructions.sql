create table combine_instructions
(
    dokon_shiji_code               varchar(5) not null,
    dokon_shiji_priority           numeric(5,0) not null,
    dokon_shiji_name               varchar(50) not null,
    web_order_kbn                  varchar(1) not null,
    delivery_type_no               numeric(8,0),
    dokon_start_date               timestamp(0) not null,
    dokon_end_date                 timestamp(0) not null,
    customer_list_connect_flg      numeric(1,0) not null,
    dokon_memo                     varchar(40),
    dokon_joken_disp               varchar(500),
    exclude_joken_disp             varchar(500),
    change_user_code               numeric(38,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_instructions_pk primary key (dokon_shiji_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table combine_instructions is '同梱指示マスタ';

comment on column combine_instructions.dokon_shiji_code is '同梱指示コード';

comment on column combine_instructions.dokon_shiji_priority is '優先順位';

comment on column combine_instructions.dokon_shiji_name is '同梱指示名称';

comment on column combine_instructions.web_order_kbn is 'WEB受注限定区分';

comment on column combine_instructions.delivery_type_no is '配送種別番号';

comment on column combine_instructions.dokon_start_date is '同梱期間開始日';

comment on column combine_instructions.dokon_end_date is '同梱期間終了日';

comment on column combine_instructions.customer_list_connect_flg is '顧客リスト自動連携フラグ';

comment on column combine_instructions.dokon_memo is '同梱メモ';

comment on column combine_instructions.dokon_joken_disp is '同梱条件（一覧表示用）';

comment on column combine_instructions.exclude_joken_disp is '除外条件（一覧表示用）';

comment on column combine_instructions.change_user_code is '入力担当ユーザコード';

comment on column combine_instructions.orm_rowid is 'データ行ID';

comment on column combine_instructions.created_user is '作成ユーザ';

comment on column combine_instructions.created_datetime is '作成日時';

comment on column combine_instructions.updated_user is '更新ユーザ';

comment on column combine_instructions.updated_datetime is '更新日時';

create unique index combine_instructions_ix0
on combine_instructions
(orm_rowid)
tablespace ts_commodity_i01;

create index combine_instructions_ix1
on combine_instructions
(web_order_kbn)
tablespace ts_commodity_i01;

create index combine_instructions_ix2
on combine_instructions
(delivery_type_no)
tablespace ts_commodity_i01;

create index combine_instructions_ix3
on combine_instructions
(dokon_start_date)
tablespace ts_commodity_i01;

create index combine_instructions_ix4
on combine_instructions
(dokon_end_date)
tablespace ts_commodity_i01;
