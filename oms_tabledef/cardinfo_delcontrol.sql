create table cardinfo_delcontrol
(
    credit_card_kanri_no           varchar(12) not null,
    credit_card_kanri_detail_no    varchar(4) not null,
    credit_card_delete_status      varchar(2),
    credit_card_delete_datetime    timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
)
tablespace ts_customer_t01;

comment on table cardinfo_delcontrol is 'カード情報削除管理ワーク';

comment on column cardinfo_delcontrol.credit_card_kanri_no is 'クレジットカードお預かり管理番号';

comment on column cardinfo_delcontrol.credit_card_kanri_detail_no is 'クレジットカードお預かり管理明細番号';

comment on column cardinfo_delcontrol.credit_card_delete_status is 'カード情報削除ステータス';

comment on column cardinfo_delcontrol.credit_card_delete_datetime is 'カード情報削除日時';

comment on column cardinfo_delcontrol.orm_rowid is 'データ行ID';

comment on column cardinfo_delcontrol.created_user is '作成ユーザ';

comment on column cardinfo_delcontrol.created_datetime is '作成日時';

comment on column cardinfo_delcontrol.updated_user is '更新ユーザ';

comment on column cardinfo_delcontrol.updated_datetime is '更新日時';

