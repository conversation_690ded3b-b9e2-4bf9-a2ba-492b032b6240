create table media_prod
(
    baitai_code                    varchar(10) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16),
    benefits_code                  varchar(10),
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint media_prod_pk primary key (baitai_code, shop_code, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table media_prod is '媒体商品紐付けマスタ';

comment on column media_prod.baitai_code is '媒体コード';

comment on column media_prod.shop_code is 'ショップコード';

comment on column media_prod.sku_code is 'SKUコード';

comment on column media_prod.commodity_code is '商品コード';

comment on column media_prod.benefits_code is '特典コード';

comment on column media_prod.display_order is '表示順';

comment on column media_prod.orm_rowid is 'データ行ID';

comment on column media_prod.created_user is '作成ユーザ';

comment on column media_prod.created_datetime is '作成日時';

comment on column media_prod.updated_user is '更新ユーザ';

comment on column media_prod.updated_datetime is '更新日時';

create unique index media_prod_ix0
on media_prod
(orm_rowid)
tablespace ts_commodity_i01;
