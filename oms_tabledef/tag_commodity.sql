create table tag_commodity
(
    shop_code                      varchar(16) not null,
    tag_code                       varchar(16) not null,
    commodity_code                 varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint tag_commodity_pk primary key (shop_code, tag_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table tag_commodity is 'タグ商品';

comment on column tag_commodity.shop_code is 'ショップコード';

comment on column tag_commodity.tag_code is 'タグコード';

comment on column tag_commodity.commodity_code is '商品コード';

comment on column tag_commodity.orm_rowid is 'データ行ID';

comment on column tag_commodity.created_user is '作成ユーザ';

comment on column tag_commodity.created_datetime is '作成日時';

comment on column tag_commodity.updated_user is '更新ユーザ';

comment on column tag_commodity.updated_datetime is '更新日時';

create unique index tag_commodity_ix0
on tag_commodity
(orm_rowid)
tablespace ts_commodity_i01;
