create table mail_magazine_subscriber
(
    mail_magazine_code             varchar(16) not null,
    email                          varchar(256) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint mail_magazine_subscriber_pk primary key (mail_magazine_code, email) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table mail_magazine_subscriber is 'メールマガジン購読者';

comment on column mail_magazine_subscriber.mail_magazine_code is 'メールマガジンコード';

comment on column mail_magazine_subscriber.email is 'メールアドレス';

comment on column mail_magazine_subscriber.orm_rowid is 'データ行ID';

comment on column mail_magazine_subscriber.created_user is '作成ユーザ';

comment on column mail_magazine_subscriber.created_datetime is '作成日時';

comment on column mail_magazine_subscriber.updated_user is '更新ユーザ';

comment on column mail_magazine_subscriber.updated_datetime is '更新日時';

create unique index mail_magazine_subscriber_ix0
on mail_magazine_subscriber
(orm_rowid)
tablespace ts_shop_i01;
