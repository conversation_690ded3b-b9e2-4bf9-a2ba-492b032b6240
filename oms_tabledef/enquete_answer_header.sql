create table enquete_answer_header
(
    customer_code                  varchar(16) not null,
    enquete_code                   varchar(16) not null,
    enquete_reply_date             timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint enquete_answer_header_pk primary key (enquete_code, customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table enquete_answer_header is 'アンケート回答ヘッダ';

comment on column enquete_answer_header.customer_code is '顧客コード';

comment on column enquete_answer_header.enquete_code is 'アンケートコード';

comment on column enquete_answer_header.enquete_reply_date is 'アンケート回答日';

comment on column enquete_answer_header.orm_rowid is 'データ行ID';

comment on column enquete_answer_header.created_user is '作成ユーザ';

comment on column enquete_answer_header.created_datetime is '作成日時';

comment on column enquete_answer_header.updated_user is '更新ユーザ';

comment on column enquete_answer_header.updated_datetime is '更新日時';

create unique index enquete_answer_header_ix0
on enquete_answer_header
(orm_rowid)
tablespace ts_customer_i01;
