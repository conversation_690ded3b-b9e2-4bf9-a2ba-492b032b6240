create table campaign_count_commodity
(
    campaign_instructions_code     varchar(16) not null,
    customer_code                  varchar(16) not null,
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    campaign_used_count            numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_count_commodity_pk primary key (campaign_instructions_code, customer_code, shop_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_count_commodity is 'キャンペーン累積商品適用件数';

comment on column campaign_count_commodity.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_count_commodity.customer_code is '顧客コード';

comment on column campaign_count_commodity.shop_code is 'ショップコード';

comment on column campaign_count_commodity.commodity_code is '商品コード';

comment on column campaign_count_commodity.campaign_used_count is 'キャンペーン累計利用回数';

comment on column campaign_count_commodity.orm_rowid is 'データ行ID';

comment on column campaign_count_commodity.created_user is '作成ユーザ';

comment on column campaign_count_commodity.created_datetime is '作成日時';

comment on column campaign_count_commodity.updated_user is '更新ユーザ';

comment on column campaign_count_commodity.updated_datetime is '更新日時';

create unique index campaign_count_commodity_ix0
on campaign_count_commodity
(orm_rowid)
tablespace ts_commodity_i01;
