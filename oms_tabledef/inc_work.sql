create table inc_work
(
    shop_code                      varchar(16) not null,
    inc_work_code                  varchar(16) not null,
    inc_work_name                  varchar(50) not null,
    inc_work_front_name            varchar(50) not null,
    inc_work_tax_type              numeric(1,0) not null,
    display_flg                    numeric(1,0) not null,
    choices_enable_flg             numeric(1,0) not null,
    price_establish_type           numeric(1,0) not null,
    free_text_enable_type          numeric(1,0) not null,
    free_text_title                varchar(50),
    free_text_necessary_flg        numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint inc_work_pk primary key (shop_code, inc_work_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table inc_work is '付帯サービス';

comment on column inc_work.shop_code is 'ショップコード';

comment on column inc_work.inc_work_code is '付帯サービスコード';

comment on column inc_work.inc_work_name is '付帯サービス名称';

comment on column inc_work.inc_work_front_name is '付帯サービスフロント名称';

comment on column inc_work.inc_work_tax_type is '付帯サービス消費税区分';

comment on column inc_work.display_flg is '表示フラグ';

comment on column inc_work.choices_enable_flg is '選択肢使用区分';

comment on column inc_work.price_establish_type is '価格設定区分';

comment on column inc_work.free_text_enable_type is '自由入力欄使用区分';

comment on column inc_work.free_text_title is '自由入力欄タイトル';

comment on column inc_work.free_text_necessary_flg is '自由入力欄必須フラグ';

comment on column inc_work.orm_rowid is 'データ行ID';

comment on column inc_work.created_user is '作成ユーザ';

comment on column inc_work.created_datetime is '作成日時';

comment on column inc_work.updated_user is '更新ユーザ';

comment on column inc_work.updated_datetime is '更新日時';

create unique index inc_work_ix0
on inc_work
(orm_rowid)
tablespace ts_commodity_i01;
