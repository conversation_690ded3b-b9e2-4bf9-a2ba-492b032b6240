create table reserved_commodity_header
(
    shop_code                      varchar(16) not null,
    reflect_datetime_code          varchar(10) not null,
    commodity_code                 varchar(16) not null,
    commodity_name                 varchar(100) not null,
    commodity_type                 numeric(1,0),
    represent_sku_code             varchar(24),
    represent_sku_unit_price       numeric(8,0),
    stock_status_no                numeric(8,0),
    stock_management_type          numeric(1,0),
    age_limit_code                 numeric(8,0),
    commodity_tax_type             numeric(1,0),
    short_description              varchar(100),
    commodity_search_words         varchar(500),
    prior_printing_description     varchar(1000),
    posterior_printing_description varchar(1000),
    delivery_description           varchar(1000),
    sale_start_datetime            timestamp(0),
    sale_end_datetime              timestamp(0),
    discount_price_start_datetime  timestamp(0),
    discount_price_end_datetime    timestamp(0),
    reservation_start_datetime     timestamp(0),
    reservation_end_datetime       timestamp(0),
    prior_printing_start_date      timestamp(0),
    prior_printing_end_date        timestamp(0),
    posterior_printing_start_date  timestamp(0),
    posterior_printing_end_date    timestamp(0),
    delivery_type_no               numeric(8,0),
    sales_method_type              numeric(1,0),
    manufacturer_model_no          varchar(50),
    link_url                       varchar(256),
    recommend_commodity_rank       numeric(8,0),
    commodity_popular_rank         numeric(8,0),
    commodity_standard1_name       varchar(20),
    commodity_standard2_name       varchar(20),
    commodity_point_rate           numeric(3,0),
    commodity_point_start_datetime timestamp(0),
    commodity_point_end_datetime   timestamp(0),
    sale_flg                       numeric(1,0),
    noshi_effective_flg            numeric(1,0),
    arrival_goods_flg              numeric(1,0),
    oneshot_order_limit            numeric(8,0),
    standard_image_type            numeric(1,0),
    purchasing_confirm_flg_pc      numeric(1,0),
    purchasing_confirm_flg_sp      numeric(1,0),
    change_reason                  varchar(50),
    reflect_flg                    numeric(1,0) not null,
    error_flg                      numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint res_commodity_header_pk primary key (shop_code, reflect_datetime_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table reserved_commodity_header is '商品情報変更予約商品ヘッダ';

comment on column reserved_commodity_header.shop_code is 'ショップコード';

comment on column reserved_commodity_header.reflect_datetime_code is '反映日時コード';

comment on column reserved_commodity_header.commodity_code is '商品コード';

comment on column reserved_commodity_header.commodity_name is '商品名称';

comment on column reserved_commodity_header.commodity_type is '商品区分';

comment on column reserved_commodity_header.represent_sku_code is '代表SKUコード';

comment on column reserved_commodity_header.represent_sku_unit_price is '代表SKU単価';

comment on column reserved_commodity_header.stock_status_no is '在庫状況番号';

comment on column reserved_commodity_header.stock_management_type is '在庫管理区分';

comment on column reserved_commodity_header.age_limit_code is '年齢制限コード';

comment on column reserved_commodity_header.commodity_tax_type is '商品消費税区分';

comment on column reserved_commodity_header.short_description is '概要説明';

comment on column reserved_commodity_header.commodity_search_words is '商品検索ワード';

comment on column reserved_commodity_header.prior_printing_description is '事前掲載説明';

comment on column reserved_commodity_header.posterior_printing_description is '事後掲載説明';

comment on column reserved_commodity_header.delivery_description is '納期説明';

comment on column reserved_commodity_header.sale_start_datetime is '販売開始日時';

comment on column reserved_commodity_header.sale_end_datetime is '販売終了日時';

comment on column reserved_commodity_header.discount_price_start_datetime is '特別価格開始日時';

comment on column reserved_commodity_header.discount_price_end_datetime is '特別価格終了日時';

comment on column reserved_commodity_header.reservation_start_datetime is '予約開始日時';

comment on column reserved_commodity_header.reservation_end_datetime is '予約終了日時';

comment on column reserved_commodity_header.prior_printing_start_date is '事前掲載開始日時';

comment on column reserved_commodity_header.prior_printing_end_date is '事前掲載終了日時';

comment on column reserved_commodity_header.posterior_printing_start_date is '事後掲載開始日時';

comment on column reserved_commodity_header.posterior_printing_end_date is '事後掲載終了日時';

comment on column reserved_commodity_header.delivery_type_no is '配送種別番号';

comment on column reserved_commodity_header.sales_method_type is '販売方法区分';

comment on column reserved_commodity_header.manufacturer_model_no is 'メーカー型番';

comment on column reserved_commodity_header.link_url is 'リンクURL';

comment on column reserved_commodity_header.recommend_commodity_rank is 'おすすめ商品順位';

comment on column reserved_commodity_header.commodity_popular_rank is '人気順位';

comment on column reserved_commodity_header.commodity_standard1_name is '規格名称1';

comment on column reserved_commodity_header.commodity_standard2_name is '規格名称2';

comment on column reserved_commodity_header.commodity_point_rate is '商品別ポイント付与率';

comment on column reserved_commodity_header.commodity_point_start_datetime is '商品別ポイント付与開始日時';

comment on column reserved_commodity_header.commodity_point_end_datetime is '商品別ポイント付与終了日時';

comment on column reserved_commodity_header.sale_flg is '販売フラグ';

comment on column reserved_commodity_header.noshi_effective_flg is '熨斗有効フラグ';

comment on column reserved_commodity_header.arrival_goods_flg is '入荷お知らせ可能フラグ';

comment on column reserved_commodity_header.oneshot_order_limit is '注文毎注文上限数';

comment on column reserved_commodity_header.standard_image_type is '規格画像区分';

comment on column reserved_commodity_header.purchasing_confirm_flg_pc is 'PC用購入確認フラグ';

comment on column reserved_commodity_header.purchasing_confirm_flg_sp is 'スマートフォン用購入確認フラグ';

comment on column reserved_commodity_header.change_reason is '変更理由';

comment on column reserved_commodity_header.reflect_flg is '反映フラグ';

comment on column reserved_commodity_header.error_flg is 'エラーフラグ';

comment on column reserved_commodity_header.orm_rowid is 'データ行ID';

comment on column reserved_commodity_header.created_user is '作成ユーザ';

comment on column reserved_commodity_header.created_datetime is '作成日時';

comment on column reserved_commodity_header.updated_user is '更新ユーザ';

comment on column reserved_commodity_header.updated_datetime is '更新日時';

create unique index res_commodity_header_ix0
on reserved_commodity_header
(orm_rowid)
tablespace ts_commodity_i01;

create unique index res_commodity_header_ix1
on reserved_commodity_header
(shop_code, commodity_code, reflect_datetime_code)
tablespace ts_commodity_i01;
