create table region_block_location
(
    shop_code                      varchar(16) not null,
    region_block_id                numeric(38,0) not null,
    prefecture_code                varchar(2) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint region_block_location_pk primary key (shop_code, prefecture_code, region_block_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table region_block_location is '地域ブロック配置';

comment on column region_block_location.shop_code is 'ショップコード';

comment on column region_block_location.region_block_id is '地域ブロックID';

comment on column region_block_location.prefecture_code is '都道府県コード';

comment on column region_block_location.orm_rowid is 'データ行ID';

comment on column region_block_location.created_user is '作成ユーザ';

comment on column region_block_location.created_datetime is '作成日時';

comment on column region_block_location.updated_user is '更新ユーザ';

comment on column region_block_location.updated_datetime is '更新日時';

create unique index region_block_location_ix0
on region_block_location
(orm_rowid)
tablespace ts_shop_i01;

create unique index region_block_location_ix1
on region_block_location
(shop_code, prefecture_code)
tablespace ts_shop_i01;
