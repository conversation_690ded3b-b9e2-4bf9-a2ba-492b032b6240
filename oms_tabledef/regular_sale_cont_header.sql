create table regular_sale_cont_header
(
    regular_contract_no            varchar(14) not null,
    shop_code                      varchar(16) not null,
    regular_sale_cont_datetime     timestamp(0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    payment_method_no              numeric(8,0) not null,
    address_no                     numeric(8,0),
    regular_sale_cont_status       numeric(1,0) not null,
    next_delivery_request_date     timestamp(0),
    external_order_no              varchar(50),
    order_user_code                numeric(38,0),
    regular_update_datetime        timestamp(0),
    change_user_code               numeric(38,0),
    regular_update_reason_kbn      varchar(2),
    otodoke_hope_time_kbn          varchar(2),
    marketing_channel              varchar(2),
    delivery_type_no               numeric(8,0),
    shipping_method_flg            numeric(1,0) not null,
    ext_payment_method_type        varchar(2) not null,
    card_brand                     varchar(2),
    credit_card_kanri_no           varchar(12),
    credit_card_kanri_detail_no    varchar(4),
    credit_card_no                 varchar(50),
    credit_card_meigi              varchar(150),
    credit_card_valid_year         varchar(4),
    credit_card_valid_month        varchar(2),
    credit_card_pay_count          varchar(2),
    amzn_charge_permission_id      text,
    bill_address_kbn               varchar(1) not null,
    bill_print_otodoke_id          varchar(10),
    o_name_disp_kbn                varchar(1) not null,
    delivery_note_flg              numeric(1,0) not null,
    include_flg                    numeric(1,0) not null,
    receipt_flg                    numeric(1,0) not null,
    receipt_to                     varchar(50),
    receipt_detail                 varchar(50),
    first_shipping_date            timestamp(0),
    lastest_shipping_date          timestamp(0),
    first_delivery_date            timestamp(0),
    lastest_delivery_date          timestamp(0),
    regular_stop_date              timestamp(0),
    regular_stop_reason_kbn        varchar(2),
    regular_hold_date              timestamp(0),
    regular_hold_clear_date        timestamp(0),
    regular_kaiji                  numeric(5,0) not null,
    shipped_regular_count          numeric(3,0) not null,
    delivery_memo                  varchar(20),
    regular_hold_reason_kbn        varchar(2),
    niyose_flg                     numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_cont_header_pk primary key (regular_contract_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table regular_sale_cont_header is '定期契約ヘッダ';

comment on column regular_sale_cont_header.regular_contract_no is '定期契約番号';

comment on column regular_sale_cont_header.shop_code is 'ショップコード';

comment on column regular_sale_cont_header.regular_sale_cont_datetime is '定期契約日時';

comment on column regular_sale_cont_header.customer_code is '顧客コード';

comment on column regular_sale_cont_header.neo_customer_no is '顧客番号';

comment on column regular_sale_cont_header.payment_method_no is '支払方法番号';

comment on column regular_sale_cont_header.address_no is 'アドレス帳番号';

comment on column regular_sale_cont_header.regular_sale_cont_status is '定期ステータス';

comment on column regular_sale_cont_header.next_delivery_request_date is '次回お届け希望日';

comment on column regular_sale_cont_header.external_order_no is '外部受注番号';

comment on column regular_sale_cont_header.order_user_code is '受注担当ユーザコード';

comment on column regular_sale_cont_header.regular_update_datetime is '定期変更日時';

comment on column regular_sale_cont_header.change_user_code is '入力担当ユーザコード';

comment on column regular_sale_cont_header.regular_update_reason_kbn is '定期変更理由';

comment on column regular_sale_cont_header.otodoke_hope_time_kbn is 'お届け希望時間帯';

comment on column regular_sale_cont_header.marketing_channel is '販売経路';

comment on column regular_sale_cont_header.delivery_type_no is '配送種別番号';

comment on column regular_sale_cont_header.shipping_method_flg is '配送方法顧客指定フラグ';

comment on column regular_sale_cont_header.ext_payment_method_type is '支払方法区分（拡張）';

comment on column regular_sale_cont_header.card_brand is 'カードブランド';

comment on column regular_sale_cont_header.credit_card_kanri_no is 'クレジットカードお預かり管理番号';

comment on column regular_sale_cont_header.credit_card_kanri_detail_no is 'クレジットカードお預かり管理明細番号';

comment on column regular_sale_cont_header.credit_card_no is 'クレジットカード番号';

comment on column regular_sale_cont_header.credit_card_meigi is 'クレジットカード名義人';

comment on column regular_sale_cont_header.credit_card_valid_year is 'クレジットカード有効期限年';

comment on column regular_sale_cont_header.credit_card_valid_month is 'クレジットカード有効期限月';

comment on column regular_sale_cont_header.credit_card_pay_count is 'クレジットカード支払回数';

comment on column regular_sale_cont_header.amzn_charge_permission_id is 'AmazonPay注文ID';

comment on column regular_sale_cont_header.bill_address_kbn is '請求先区分';

comment on column regular_sale_cont_header.bill_print_otodoke_id is '請求お届け先ＩＤ';

comment on column regular_sale_cont_header.o_name_disp_kbn is '依頼主＿名前表示区分';

comment on column regular_sale_cont_header.delivery_note_flg is '納品書不要フラグ';

comment on column regular_sale_cont_header.include_flg is '同梱不要フラグ';

comment on column regular_sale_cont_header.receipt_flg is '領収書フラグ';

comment on column regular_sale_cont_header.receipt_to is '領収書宛名';

comment on column regular_sale_cont_header.receipt_detail is '領収書但し書き';

comment on column regular_sale_cont_header.first_shipping_date is '初回発送日';

comment on column regular_sale_cont_header.lastest_shipping_date is '最新発送日';

comment on column regular_sale_cont_header.first_delivery_date is '初回お届け日';

comment on column regular_sale_cont_header.lastest_delivery_date is '最新お届け日';

comment on column regular_sale_cont_header.regular_stop_date is '定期休止日';

comment on column regular_sale_cont_header.regular_stop_reason_kbn is '定期休止理由区分';

comment on column regular_sale_cont_header.regular_hold_date is '定期保留日';

comment on column regular_sale_cont_header.regular_hold_clear_date is '定期保留解除日';

comment on column regular_sale_cont_header.regular_kaiji is '定期回次';

comment on column regular_sale_cont_header.shipped_regular_count is '発送済定期回数';

comment on column regular_sale_cont_header.delivery_memo is '配送メモ';

comment on column regular_sale_cont_header.regular_hold_reason_kbn is '定期保留理由区分';

comment on column regular_sale_cont_header.niyose_flg is '荷寄せ可能フラグ';

comment on column regular_sale_cont_header.orm_rowid is 'データ行ID';

comment on column regular_sale_cont_header.created_user is '作成ユーザ';

comment on column regular_sale_cont_header.created_datetime is '作成日時';

comment on column regular_sale_cont_header.updated_user is '更新ユーザ';

comment on column regular_sale_cont_header.updated_datetime is '更新日時';

create unique index regular_sale_cont_header_ix0
on regular_sale_cont_header
(orm_rowid)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix1
on regular_sale_cont_header
(customer_code, regular_hold_date, regular_hold_clear_date)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix2
on regular_sale_cont_header
(regular_sale_cont_datetime)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix3
on regular_sale_cont_header
(neo_customer_no)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix4
on regular_sale_cont_header
(credit_card_kanri_no)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix5
on regular_sale_cont_header
(regular_sale_cont_status)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix6
on regular_sale_cont_header
(change_user_code)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix7
on regular_sale_cont_header
(ext_payment_method_type)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix8
on regular_sale_cont_header
(regular_stop_date)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix9
on regular_sale_cont_header
(regular_hold_date)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix10
on regular_sale_cont_header
(regular_hold_clear_date)
tablespace ts_order_i01;

create index regular_sale_cont_header_ix11
on regular_sale_cont_header
(regular_kaiji)
tablespace ts_order_i01;
