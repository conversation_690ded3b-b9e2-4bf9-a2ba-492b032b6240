create table campaign_instructions
(
    campaign_instructions_code     varchar(16) not null,
    campaign_instructions_name     varchar(50) not null,
    campaign_type                  varchar(2),
    delete_flg                     numeric(1,0) not null,
    campaign_priority              numeric(5,0),
    campaign_applied_scope         varchar(2) not null,
    campaign_use_limit             varchar(2) not null,
    oneshot_order_limit            numeric(8,0),
    campaign_quantity_limit        numeric(8,0),
    campaign_start_date            timestamp(0) not null,
    campaign_end_date              timestamp(0) not null,
    present_use_flg                numeric(1,0) not null,
    campaign_customer_flg          numeric(1,0) not null,
    campaign_combi_limit_flg       numeric(1,0) not null,
    permanent_campaign_flg         numeric(1,0) not null,
    baitai_code                    varchar(10),
    campaign_description           varchar(100),
    change_user_code               numeric(38,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_instructions_pk primary key (campaign_instructions_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_instructions is 'キャンペーン設定';

comment on column campaign_instructions.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_instructions.campaign_instructions_name is 'キャンペーン設定名称';

comment on column campaign_instructions.campaign_type is 'キャンペーン種別';

comment on column campaign_instructions.delete_flg is '削除フラグ';

comment on column campaign_instructions.campaign_priority is 'キャンペーン優先順位';

comment on column campaign_instructions.campaign_applied_scope is 'キャンペーン適用範囲';

comment on column campaign_instructions.campaign_use_limit is 'キャンペーン利用制限';

comment on column campaign_instructions.oneshot_order_limit is '注文毎注文上限数';

comment on column campaign_instructions.campaign_quantity_limit is 'キャンペーン累積受注上限数';

comment on column campaign_instructions.campaign_start_date is 'キャンペーン適用開始日';

comment on column campaign_instructions.campaign_end_date is 'キャンペーン適用終了日';

comment on column campaign_instructions.present_use_flg is '選択式プレゼント利用フラグ';

comment on column campaign_instructions.campaign_customer_flg is 'キャンペーン会員指定フラグ';

comment on column campaign_instructions.campaign_combi_limit_flg is 'キャンペーン併用制限フラグ';

comment on column campaign_instructions.permanent_campaign_flg is '常設キャンペーンフラグ';

comment on column campaign_instructions.baitai_code is '媒体コード';

comment on column campaign_instructions.campaign_description is 'キャンペーン内容';

comment on column campaign_instructions.change_user_code is '入力担当ユーザコード';

comment on column campaign_instructions.orm_rowid is 'データ行ID';

comment on column campaign_instructions.created_user is '作成ユーザ';

comment on column campaign_instructions.created_datetime is '作成日時';

comment on column campaign_instructions.updated_user is '更新ユーザ';

comment on column campaign_instructions.updated_datetime is '更新日時';

create unique index campaign_instructions_ix0
on campaign_instructions
(orm_rowid)
tablespace ts_commodity_i01;

create index campaign_instructions_ix1
on campaign_instructions
(campaign_start_date, campaign_end_date, campaign_customer_flg)
tablespace ts_commodity_i01;
