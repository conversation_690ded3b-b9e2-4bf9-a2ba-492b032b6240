create table coupon
(
    coupon_management_code         varchar(16) not null,
    coupon_code                    varchar(16),
    coupon_name                    varchar(50) not null,
    coupon_invalid_flag            numeric(1,0) not null,
    coupon_type                    numeric(1,0) not null,
    coupon_issue_type              numeric(1,0) not null,
    coupon_use_limit               numeric(1,0) not null,
    coupon_use_purchase_price      numeric(8,0) not null,
    coupon_discount_type           numeric(1,0) not null,
    coupon_discount_rate           numeric(3,0),
    coupon_discount_price          numeric(8,0),
    coupon_start_datetime          timestamp(0) not null,
    coupon_end_datetime            timestamp(0) not null,
    coupon_limit_display_period    numeric(3,0),
    coupon_limit_display           varchar(50),
    coupon_description             varchar(200),
    coupon_message                 varchar(200),
    coupon_kbn                     varchar(2),
    coupon_post_in_charge          varchar(16) not null,
    coupon_commodity_flag          numeric(1,0) not null,
    marketing_channel_list         varchar(100),
    goods_group                    varchar(16),
    commodity_category_code        varchar(16),
    commodity_series               varchar(5),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint coupon_pk primary key (coupon_management_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table coupon is 'クーポン';

comment on column coupon.coupon_management_code is 'クーポン管理コード';

comment on column coupon.coupon_code is 'クーポンコード';

comment on column coupon.coupon_name is 'クーポン名称';

comment on column coupon.coupon_invalid_flag is 'クーポン無効フラグ';

comment on column coupon.coupon_type is 'クーポン種別';

comment on column coupon.coupon_issue_type is 'クーポン自動発行種別';

comment on column coupon.coupon_use_limit is 'クーポン利用制限';

comment on column coupon.coupon_use_purchase_price is 'クーポン利用最低購入金額';

comment on column coupon.coupon_discount_type is 'クーポン値引区分';

comment on column coupon.coupon_discount_rate is 'クーポン値引率';

comment on column coupon.coupon_discount_price is 'クーポン値引額';

comment on column coupon.coupon_start_datetime is 'クーポン開始日時';

comment on column coupon.coupon_end_datetime is 'クーポン終了日時';

comment on column coupon.coupon_limit_display_period is 'クーポン期限表示日数';

comment on column coupon.coupon_limit_display is 'クーポン期限表示説明';

comment on column coupon.coupon_description is 'クーポン説明';

comment on column coupon.coupon_message is '連絡事項';

comment on column coupon.coupon_kbn is 'クーポン区分';

comment on column coupon.coupon_post_in_charge is 'クーポン担当部署';

comment on column coupon.coupon_commodity_flag is 'クーポン特定商品指定フラグ';

comment on column coupon.marketing_channel_list is '販売経路指定';

comment on column coupon.goods_group is 'クーポン部門';

comment on column coupon.commodity_category_code is 'クーポン大分類';

comment on column coupon.commodity_series is 'クーポン商品シリーズ';

comment on column coupon.orm_rowid is 'データ行ID';

comment on column coupon.created_user is '作成ユーザ';

comment on column coupon.created_datetime is '作成日時';

comment on column coupon.updated_user is '更新ユーザ';

comment on column coupon.updated_datetime is '更新日時';

create unique index coupon_ix0
on coupon
(orm_rowid)
tablespace ts_customer_i01;

create index coupon_ix1
on coupon
(coupon_start_datetime, coupon_end_datetime, coupon_type, coupon_use_limit)
tablespace ts_customer_i01;
