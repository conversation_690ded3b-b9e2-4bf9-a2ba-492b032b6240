create table privilege
(
    benefits_code                  varchar(10) not null,
    benefits_name                  varchar(50) not null,
    benefits_memo                  varchar(100),
    start_datetime                 timestamp(0) not null,
    end_datetime                   timestamp(0) not null,
    tokuten_exclude_flg            numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint privilege_pk primary key (benefits_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table privilege is '特典マスタ';

comment on column privilege.benefits_code is '特典コード';

comment on column privilege.benefits_name is '特典名称';

comment on column privilege.benefits_memo is '特典メモ';

comment on column privilege.start_datetime is '適用開始日時';

comment on column privilege.end_datetime is '適用終了日時';

comment on column privilege.tokuten_exclude_flg is '特典除外フラグ';

comment on column privilege.orm_rowid is 'データ行ID';

comment on column privilege.created_user is '作成ユーザ';

comment on column privilege.created_datetime is '作成日時';

comment on column privilege.updated_user is '更新ユーザ';

comment on column privilege.updated_datetime is '更新日時';

create unique index privilege_ix0
on privilege
(orm_rowid)
tablespace ts_shop_i01;
