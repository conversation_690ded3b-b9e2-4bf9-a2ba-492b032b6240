create table regular_sale_cont_composition
(
    regular_contract_no            varchar(14) not null,
    regular_contract_detail_no     numeric(3,0) not null,
    composition_no                 numeric(2,0) not null,
    shop_code                      varchar(16) not null,
    parent_commodity_code          varchar(16) not null,
    parent_sku_code                varchar(24) not null,
    child_commodity_code           varchar(16) not null,
    child_sku_code                 varchar(24) not null,
    commodity_name                 varchar(100) not null,
    composition_quantity           numeric(2,0) not null,
    regular_sale_composition_no    varchar(8) not null,
    regular_sale_composition_name  varchar(50),
    regular_sale_commodity_type    varchar(1),
    regular_order_count_min_limit  numeric(5,0),
    regular_order_count_max_limit  numeric(5,0),
    regular_order_count_interval   numeric(5,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_cont_composition_pk primary key (regular_contract_no, regular_contract_detail_no, composition_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table regular_sale_cont_composition is '定期契約明細構成品';

comment on column regular_sale_cont_composition.regular_contract_no is '定期契約番号';

comment on column regular_sale_cont_composition.regular_contract_detail_no is '定期契約明細番号';

comment on column regular_sale_cont_composition.composition_no is '構成品項番';

comment on column regular_sale_cont_composition.shop_code is 'ショップコード';

comment on column regular_sale_cont_composition.parent_commodity_code is '親商品コード';

comment on column regular_sale_cont_composition.parent_sku_code is '親SKUコード';

comment on column regular_sale_cont_composition.child_commodity_code is '子商品コード';

comment on column regular_sale_cont_composition.child_sku_code is '子SKUコード';

comment on column regular_sale_cont_composition.commodity_name is '商品名称';

comment on column regular_sale_cont_composition.composition_quantity is '構成数量';

comment on column regular_sale_cont_composition.regular_sale_composition_no is '定期便構成グループコード';

comment on column regular_sale_cont_composition.regular_sale_composition_name is '定期便構成名称';

comment on column regular_sale_cont_composition.regular_sale_commodity_type is '定期便商品構成区分';

comment on column regular_sale_cont_composition.regular_order_count_min_limit is '定期回次下限';

comment on column regular_sale_cont_composition.regular_order_count_max_limit is '定期回次上限';

comment on column regular_sale_cont_composition.regular_order_count_interval is '定期回次間隔';

comment on column regular_sale_cont_composition.orm_rowid is 'データ行ID';

comment on column regular_sale_cont_composition.created_user is '作成ユーザ';

comment on column regular_sale_cont_composition.created_datetime is '作成日時';

comment on column regular_sale_cont_composition.updated_user is '更新ユーザ';

comment on column regular_sale_cont_composition.updated_datetime is '更新日時';

create unique index regular_sale_cont_composition_ix0
on regular_sale_cont_composition
(orm_rowid)
tablespace ts_order_i01;

create index regular_sale_cont_composition_ix1
on regular_sale_cont_composition
(shop_code)
tablespace ts_order_i01;

create index regular_sale_cont_composition_ix2
on regular_sale_cont_composition
(regular_order_count_min_limit)
tablespace ts_order_i01;

create index regular_sale_cont_composition_ix3
on regular_sale_cont_composition
(regular_order_count_max_limit)
tablespace ts_order_i01;

create index regular_sale_cont_composition_ix4
on regular_sale_cont_composition
(regular_order_count_interval)
tablespace ts_order_i01;
