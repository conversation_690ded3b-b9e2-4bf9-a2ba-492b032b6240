create table broadcast_mailqueue_header
(
    mail_queue_id                  numeric(38,0) not null,
    mail_type                      varchar(2) not null,
    mail_content_type              numeric(1,0) not null,
    mail_subject                   varchar(100) not null,
    mail_sender_name               varchar(50),
    from_address                   varchar(256) not null,
    mail_text                      text not null,
    mail_send_status               numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint broadcast_mailqueue_header_pk primary key (mail_queue_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table broadcast_mailqueue_header is '同報配信メールキューヘッダ';

comment on column broadcast_mailqueue_header.mail_queue_id is 'メールキューID';

comment on column broadcast_mailqueue_header.mail_type is 'メールタイプ';

comment on column broadcast_mailqueue_header.mail_content_type is 'メールコンテンツタイプ';

comment on column broadcast_mailqueue_header.mail_subject is 'メール件名';

comment on column broadcast_mailqueue_header.mail_sender_name is '差出人名';

comment on column broadcast_mailqueue_header.from_address is 'FROMアドレス';

comment on column broadcast_mailqueue_header.mail_text is 'メール本文';

comment on column broadcast_mailqueue_header.mail_send_status is 'メール送信ステータス';

comment on column broadcast_mailqueue_header.orm_rowid is 'データ行ID';

comment on column broadcast_mailqueue_header.created_user is '作成ユーザ';

comment on column broadcast_mailqueue_header.created_datetime is '作成日時';

comment on column broadcast_mailqueue_header.updated_user is '更新ユーザ';

comment on column broadcast_mailqueue_header.updated_datetime is '更新日時';

create unique index broadcast_mailqueue_header_ix0
on broadcast_mailqueue_header
(orm_rowid)
tablespace ts_order_i01;

create index broadcast_mailqueue_header_ix1
on broadcast_mailqueue_header
(mail_type, mail_send_status)
tablespace ts_order_i01;
