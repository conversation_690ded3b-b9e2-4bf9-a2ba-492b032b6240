create table work_delivery_note_detail
(
    slip_ino                       varchar(14) not null,
    print_seq                      numeric(3,0) not null,
    goods_code                     varchar(10),
    goods_name                     varchar(100),
    qty                            numeric(7,0),
    amt                            numeric(10,0),
    amt_mark                       varchar(1),
    remark_text                    varchar(20),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_delivery_note_detail_pk primary key (slip_ino, print_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_delivery_note_detail is '納品書明細ワーク';

comment on column work_delivery_note_detail.slip_ino is '基幹出荷指示番号';

comment on column work_delivery_note_detail.print_seq is 'シーケンス';

comment on column work_delivery_note_detail.goods_code is '商品番号';

comment on column work_delivery_note_detail.goods_name is '商品名';

comment on column work_delivery_note_detail.qty is '数量';

comment on column work_delivery_note_detail.amt is '金額';

comment on column work_delivery_note_detail.amt_mark is 'マーク';

comment on column work_delivery_note_detail.remark_text is '備考';

comment on column work_delivery_note_detail.orm_rowid is 'データ行ID';

comment on column work_delivery_note_detail.created_user is '作成ユーザ';

comment on column work_delivery_note_detail.created_datetime is '作成日時';

comment on column work_delivery_note_detail.updated_user is '更新ユーザ';

comment on column work_delivery_note_detail.updated_datetime is '更新日時';

create unique index work_delivery_note_detail_ix0
on work_delivery_note_detail
(orm_rowid)
tablespace ts_order_i01;
