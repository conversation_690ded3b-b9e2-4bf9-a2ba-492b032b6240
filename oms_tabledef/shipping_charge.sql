create table shipping_charge
(
    shop_code                      varchar(16) not null,
    delivery_type_no               numeric(8,0) not null,
    region_block_id                numeric(38,0) not null,
    lead_time                      numeric(2,0) not null,
    shipping_charge                numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_charge_pk primary key (shop_code, delivery_type_no, region_block_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table shipping_charge is '送料';

comment on column shipping_charge.shop_code is 'ショップコード';

comment on column shipping_charge.delivery_type_no is '配送種別番号';

comment on column shipping_charge.region_block_id is '地域ブロックID';

comment on column shipping_charge.lead_time is '配送リードタイム';

comment on column shipping_charge.shipping_charge is '送料';

comment on column shipping_charge.orm_rowid is 'データ行ID';

comment on column shipping_charge.created_user is '作成ユーザ';

comment on column shipping_charge.created_datetime is '作成日時';

comment on column shipping_charge.updated_user is '更新ユーザ';

comment on column shipping_charge.updated_datetime is '更新日時';

create unique index shipping_charge_ix0
on shipping_charge
(orm_rowid)
tablespace ts_shop_i01;
