create table sagawa_sv_level
(
    postal_code                    varchar(7) not null,
    city_jis_code                  varchar(5),
    payment_store_code             varchar(16),
    sales_store_code               varchar(16),
    local_code                     varchar(16),
    delivery_time_ng_flg           numeric(1,0),
    cash_on_delivery_ng_flg        numeric(1,0),
    redelivery_ng_flg              numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint sagawa_sv_level_pk primary key (postal_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table sagawa_sv_level is '佐川サービスレベルマスタ';

comment on column sagawa_sv_level.postal_code is '郵便番号';

comment on column sagawa_sv_level.city_jis_code is '市区町村コード';

comment on column sagawa_sv_level.payment_store_code is '支払営業所番号';

comment on column sagawa_sv_level.sales_store_code is '売上営業所番号';

comment on column sagawa_sv_level.local_code is 'ローカルコード';

comment on column sagawa_sv_level.delivery_time_ng_flg is '時間帯指定不可フラグ';

comment on column sagawa_sv_level.cash_on_delivery_ng_flg is '代引き不可フラグ';

comment on column sagawa_sv_level.redelivery_ng_flg is '再配達不可フラグ';

comment on column sagawa_sv_level.orm_rowid is 'データ行ID';

comment on column sagawa_sv_level.created_user is '作成ユーザ';

comment on column sagawa_sv_level.created_datetime is '作成日時';

comment on column sagawa_sv_level.updated_user is '更新ユーザ';

comment on column sagawa_sv_level.updated_datetime is '更新日時';

create unique index sagawa_sv_level_ix0
on sagawa_sv_level
(orm_rowid)
tablespace ts_shop_i01;
