create table customer
(
    customer_code                  varchar(16) not null,
    customer_no                    varchar(12) not null,
    customer_group_code            varchar(16) not null,
    last_name                      varchar(20) not null,
    first_name                     varchar(20),
    last_name_kana                 varchar(40) not null,
    first_name_kana                varchar(40),
    login_id                       varchar(256) not null,
    email                          varchar(256),
    password                       varchar(128) not null,
    birth_date                     timestamp(0) not null,
    sex                            numeric(1,0) not null,
    request_mail_type              numeric(1,0) not null,
    client_mail_type               numeric(1,0) not null,
    caution                        varchar(200),
    login_datetime                 timestamp(0),
    login_error_count              numeric(2,0) not null,
    login_locked_flg               numeric(1,0) not null,
    customer_status                numeric(1,0) not null,
    customer_valid_status          varchar(2) not null,
    customer_attribute_reply_date  timestamp(0),
    latest_point_acquired_date     timestamp(0),
    rest_point                     numeric(8,0),
    temporary_point                numeric(8,0),
    withdrawal_request_date        timestamp(0),
    withdrawal_date                timestamp(0),
    auth_secret_key                varchar(128),
    customer_type                  varchar(2) not null,
    black_customer_kbn             varchar(1) not null,
    black_reason_kbn               varchar(2),
    black_register_date            timestamp(0),
    mail_advisability_flg          varchar(2) not null,
    bd_advisability_flg            varchar(2) not null,
    mail_magazine_flg              numeric(1,0) DEFAULT 0 not null,
    shipped_mail_flg               numeric(1,0) not null,
    receipt_to                     varchar(50),
    receipt_detail                 varchar(50),
    demand_exclude_flg             numeric(1,0) not null,
    crm_customer_id                varchar(20),
    unity_customer_code            varchar(16),
    unity_datetime                 timestamp(0),
    niyose_flg                     numeric(1,0) not null,
    shipping_method_kbn            varchar(2),
    ec_login_id                    varchar(256),
    guest_flg                      numeric(1,0),
    member_no                      varchar(10),
    member_status                  varchar(20),
    shortage_declare_flg           numeric(1,0),
    order_monitor_flg              numeric(1,0),
    member_memo                    varchar(1000),
    crm_customer_updated_datetime  varchar(24),
    member_rank                    varchar(1),
    order_ng_flg                   numeric(1,0),
    free_only_purchase_flg         numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_pk primary key (customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer is '顧客';

comment on column customer.customer_code is '顧客コード';

comment on column customer.customer_no is '顧客番号';

comment on column customer.customer_group_code is '顧客グループコード';

comment on column customer.last_name is '姓';

comment on column customer.first_name is '名';

comment on column customer.last_name_kana is '姓かな';

comment on column customer.first_name_kana is '名かな';

comment on column customer.login_id is 'ログインID';

comment on column customer.email is 'メールアドレス';

comment on column customer.password is 'パスワード';

comment on column customer.birth_date is '生年月日';

comment on column customer.sex is '性別';

comment on column customer.request_mail_type is '希望メール区分';

comment on column customer.client_mail_type is 'クライアントメール区分';

comment on column customer.caution is '注意事項（管理側のみ参照）';

comment on column customer.login_datetime is 'ログイン日時';

comment on column customer.login_error_count is 'ログイン失敗回数';

comment on column customer.login_locked_flg is 'ログインロックフラグ';

comment on column customer.customer_status is '顧客ステータス';

comment on column customer.customer_valid_status is '顧客有効ステータス';

comment on column customer.customer_attribute_reply_date is '顧客属性回答日';

comment on column customer.latest_point_acquired_date is 'ポイント最終獲得日';

comment on column customer.rest_point is 'ポイント残高';

comment on column customer.temporary_point is '仮発行ポイント';

comment on column customer.withdrawal_request_date is '退会希望日';

comment on column customer.withdrawal_date is '退会日';

comment on column customer.auth_secret_key is '認証シークレットキー';

comment on column customer.customer_type is '顧客種別';

comment on column customer.black_customer_kbn is 'ブラック顧客区分';

comment on column customer.black_reason_kbn is 'ブラック理由区分';

comment on column customer.black_register_date is 'ブラック登録日';

comment on column customer.mail_advisability_flg is 'メール可否区分';

comment on column customer.bd_advisability_flg is 'BDクーポン可否区分';

comment on column customer.mail_magazine_flg is 'メールマガジン配信設定フラグ';

comment on column customer.shipped_mail_flg is '発送メール配信設定フラグ';

comment on column customer.receipt_to is '領収書宛名';

comment on column customer.receipt_detail is '領収書但し書き';

comment on column customer.demand_exclude_flg is '督促除外フラグ';

comment on column customer.crm_customer_id is 'CRM顧客ID';

comment on column customer.unity_customer_code is '名寄せ統合先顧客コード';

comment on column customer.unity_datetime is '名寄せ統合日時';

comment on column customer.niyose_flg is '荷寄せ可能フラグ';

comment on column customer.shipping_method_kbn is '配送方法指定区分';

comment on column customer.ec_login_id is 'ECログインID';

comment on column customer.guest_flg is 'ゲストフラグ';

comment on column customer.member_no is '会員番号';

comment on column customer.member_status is '会員ステータス';

comment on column customer.shortage_declare_flg is '不足申告フラグ';

comment on column customer.order_monitor_flg is '注文監視フラグ';

comment on column customer.member_memo is '会員メモ';

comment on column customer.crm_customer_updated_datetime is 'CRM顧客更新日時';

comment on column customer.member_rank is '会員ランク';

comment on column customer.order_ng_flg is '注文不可フラグ';

comment on column customer.free_only_purchase_flg is 'フリーのみ購入可フラグ';

comment on column customer.orm_rowid is 'データ行ID';

comment on column customer.created_user is '作成ユーザ';

comment on column customer.created_datetime is '作成日時';

comment on column customer.updated_user is '更新ユーザ';

comment on column customer.updated_datetime is '更新日時';

create unique index customer_ix0
on customer
(orm_rowid)
tablespace ts_customer_i01;

create unique index customer_ix1
on customer
(login_id)
tablespace ts_customer_i01;

create index customer_ix3
on customer
(customer_group_code)
tablespace ts_customer_i01;

create index customer_ix4
on customer
((last_name || coalesce(first_name,'')), (last_name_kana || coalesce(first_name_kana,'')))
tablespace ts_customer_i01;

create index customer_ix5
on customer
(customer_status)
tablespace ts_customer_i01;

create unique index customer_ix6
on customer
(customer_no)
tablespace ts_customer_i01;

create index customer_ix7
on customer
(last_name)
tablespace ts_customer_i01;

create index customer_ix8
on customer
(first_name)
tablespace ts_customer_i01;

create index customer_ix9
on customer
(last_name_kana)
tablespace ts_customer_i01;

create index customer_ix10
on customer
(first_name_kana)
tablespace ts_customer_i01;

create index customer_ix11
on customer
(crm_customer_id)
tablespace ts_customer_i01;
