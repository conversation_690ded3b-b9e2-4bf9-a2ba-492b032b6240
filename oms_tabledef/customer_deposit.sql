create table customer_deposit
(
    customer_code                  varchar(16) not null,
    last_deposit_datetime          timestamp(0) not null,
    customer_deposit_total         numeric(10,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_deposit_pk primary key (customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_deposit is '顧客別預り金';

comment on column customer_deposit.customer_code is '顧客コード';

comment on column customer_deposit.last_deposit_datetime is '最終発生日時';

comment on column customer_deposit.customer_deposit_total is '顧客別預り金額残合計';

comment on column customer_deposit.orm_rowid is 'データ行ID';

comment on column customer_deposit.created_user is '作成ユーザ';

comment on column customer_deposit.created_datetime is '作成日時';

comment on column customer_deposit.updated_user is '更新ユーザ';

comment on column customer_deposit.updated_datetime is '更新日時';

create unique index customer_deposit_ix0
on customer_deposit
(orm_rowid)
tablespace ts_customer_i01;
