create table deposit_use_history
(
    deposit_use_no                 numeric(10,0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    deposit_use_datetime           timestamp(0) not null,
    deposit_use_reason_kbn         varchar(1) not null,
    deposit_use_amount             numeric(8,0) not null,
    deposit_use_henkin_no          varchar(10),
    depoist_use_order_no           varchar(16),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint deposit_use_history_pk primary key (deposit_use_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table deposit_use_history is '預り金使用履歴';

comment on column deposit_use_history.deposit_use_no is '預り金使用履歴番号';

comment on column deposit_use_history.customer_code is '顧客コード';

comment on column deposit_use_history.neo_customer_no is '顧客番号';

comment on column deposit_use_history.deposit_use_datetime is '預り金使用日時';

comment on column deposit_use_history.deposit_use_reason_kbn is '預り金使用理由';

comment on column deposit_use_history.deposit_use_amount is '預り金使用金額';

comment on column deposit_use_history.deposit_use_henkin_no is '預り金使用先＿返金処理番号';

comment on column deposit_use_history.depoist_use_order_no is '預り金使用先＿受注番号';

comment on column deposit_use_history.orm_rowid is 'データ行ID';

comment on column deposit_use_history.created_user is '作成ユーザ';

comment on column deposit_use_history.created_datetime is '作成日時';

comment on column deposit_use_history.updated_user is '更新ユーザ';

comment on column deposit_use_history.updated_datetime is '更新日時';

create unique index deposit_use_history_ix0
on deposit_use_history
(orm_rowid)
tablespace ts_customer_i01;

create index deposit_use_history_ix1
on deposit_use_history
(depoist_use_order_no)
tablespace ts_customer_i01;
