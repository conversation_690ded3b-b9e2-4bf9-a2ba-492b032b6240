create table tax_header
(
    tax_group_code                 varchar(8) not null,
    tax_name                       varchar(50) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint tax_header_pk primary key (tax_group_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table tax_header is '消費税ヘッダ';

comment on column tax_header.tax_group_code is '消費税グループコード';

comment on column tax_header.tax_name is '消費税率名';

comment on column tax_header.orm_rowid is 'データ行ID';

comment on column tax_header.created_user is '作成ユーザ';

comment on column tax_header.created_datetime is '作成日時';

comment on column tax_header.updated_user is '更新ユーザ';

comment on column tax_header.updated_datetime is '更新日時';

create unique index tax_header_ix0
on tax_header
(orm_rowid)
tablespace ts_shop_i01;
