create table payment_method
(
    shop_code                      varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    payment_method_name            varchar(25) not null,
    payment_method_display_type    numeric(1,0),
    merchant_id                    varchar(100),
    token_api_key                  varchar(100),
    secret_key                     varchar(100),
    merchant_shop_id               varchar(13) not null,
    merchant_shop_pass             varchar(20),
    payment_limit_days             numeric(3,0),
    payment_limit_amount           numeric(8,0),
    payment_method_type            varchar(2) not null,
    ext_payment_method_type        varchar(2) not null,
    advance_later_flg              numeric(1,0) not null,
    payment_commission_tax_type    numeric(1,0) not null,
    payment_commission_tax_rate    numeric(3,0),
    cvs_enable_type                numeric(1,0),
    digital_cash_enable_type       numeric(1,0),
    banking_description            varchar(24),
    banking_description_kana       varchar(24),
    career_enable_keys             varchar(15),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint payment_method_pk primary key (shop_code, payment_method_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table payment_method is '支払方法';

comment on column payment_method.shop_code is 'ショップコード';

comment on column payment_method.payment_method_no is '支払方法番号';

comment on column payment_method.payment_method_name is '支払方法名称';

comment on column payment_method.payment_method_display_type is '支払方法表示区分';

comment on column payment_method.merchant_id is 'マーチャントID';

comment on column payment_method.token_api_key is 'トークンAPIキー';

comment on column payment_method.secret_key is '接続パスワード';

comment on column payment_method.merchant_shop_id is '店舗ID';

comment on column payment_method.merchant_shop_pass is '店舗パスワード';

comment on column payment_method.payment_limit_days is '支払期限日数';

comment on column payment_method.payment_limit_amount is '支払上限金額';

comment on column payment_method.payment_method_type is '支払方法区分';

comment on column payment_method.ext_payment_method_type is '支払方法区分（拡張）';

comment on column payment_method.advance_later_flg is '先後払フラグ';

comment on column payment_method.payment_commission_tax_type is '支払手数料消費税区分';

comment on column payment_method.payment_commission_tax_rate is '支払手数料消費税率';

comment on column payment_method.cvs_enable_type is 'コンビニ使用区分';

comment on column payment_method.digital_cash_enable_type is '電子マネー使用区分';

comment on column payment_method.banking_description is 'オンライン銀行決済請求内容説明';

comment on column payment_method.banking_description_kana is 'オンライン銀行決済請求内容説明カナ';

comment on column payment_method.career_enable_keys is '携帯キャリア種別使用区分';

comment on column payment_method.orm_rowid is 'データ行ID';

comment on column payment_method.created_user is '作成ユーザ';

comment on column payment_method.created_datetime is '作成日時';

comment on column payment_method.updated_user is '更新ユーザ';

comment on column payment_method.updated_datetime is '更新日時';

create unique index payment_method_ix0
on payment_method
(orm_rowid)
tablespace ts_shop_i01;
