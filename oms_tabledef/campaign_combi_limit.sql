create table campaign_combi_limit
(
    campaign_instructions_code     varchar(16) not null,
    campaign_combi_limit_code      varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_combi_limit_pk primary key (campaign_instructions_code, campaign_combi_limit_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_combi_limit is 'キャンペーン併用不可';

comment on column campaign_combi_limit.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_combi_limit.campaign_combi_limit_code is '併用不可キャンペーン設定コード';

comment on column campaign_combi_limit.orm_rowid is 'データ行ID';

comment on column campaign_combi_limit.created_user is '作成ユーザ';

comment on column campaign_combi_limit.created_datetime is '作成日時';

comment on column campaign_combi_limit.updated_user is '更新ユーザ';

comment on column campaign_combi_limit.updated_datetime is '更新日時';

create unique index campaign_combi_limit_ix0
on campaign_combi_limit
(orm_rowid)
tablespace ts_commodity_i01;
