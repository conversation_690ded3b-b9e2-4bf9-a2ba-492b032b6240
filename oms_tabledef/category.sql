create table category
(
    category_code                  varchar(16) not null,
    category_name_pc               varchar(20) not null,
    category_name_sp               varchar(10) not null,
    parent_category_code           varchar(16) not null,
    path                           varchar(256) not null,
    depth                          numeric(2,0) not null,
    display_order                  numeric(8,0) not null,
    commodity_count                numeric(12,0),
    last_related_count_datetime    timestamp(0),
    public_commodity_count         numeric(12,0),
    last_public_count_datetime     timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint category_ix0 unique (orm_rowid) using index
        tablespace ts_commodity_i01,
    constraint category_pk primary key (category_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table category is 'カテゴリ';

comment on column category.category_code is 'カテゴリコード';

comment on column category.category_name_pc is 'PC用カテゴリ名称';

comment on column category.category_name_sp is 'スマートフォン用カテゴリ名称';

comment on column category.parent_category_code is '親カテゴリコード';

comment on column category.path is 'パス';

comment on column category.depth is '階層';

comment on column category.display_order is '表示順';

comment on column category.commodity_count is '商品件数';

comment on column category.last_related_count_datetime is '関連数最終集計日時';

comment on column category.public_commodity_count is '公開商品件数';

comment on column category.last_public_count_datetime is '公開数最終集計日時';

comment on column category.orm_rowid is 'データ行ID';

comment on column category.created_user is '作成ユーザ';

comment on column category.created_datetime is '作成日時';

comment on column category.updated_user is '更新ユーザ';

comment on column category.updated_datetime is '更新日時';

create index category_ix1
on category
(parent_category_code)
tablespace ts_commodity_i01;

create unique index category_lpk
on category
(lower(category_code))
tablespace ts_commodity_i01;
