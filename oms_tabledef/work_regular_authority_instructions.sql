create table work_regular_authority_instructions
(
    order_no                       varchar(16) not null,
    ext_payment_method_type        varchar(2),
    bill_price                     numeric(10,0),
    neo_customer_no                varchar(12),
    authority_process_flg          numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_regular_authority_instructions_pk primary key (order_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_regular_authority_instructions is '定期販売受注オーソリ指示ワーク';

comment on column work_regular_authority_instructions.order_no is '受注番号';

comment on column work_regular_authority_instructions.ext_payment_method_type is '支払方法区分（拡張）';

comment on column work_regular_authority_instructions.bill_price is '請求額';

comment on column work_regular_authority_instructions.neo_customer_no is '顧客番号';

comment on column work_regular_authority_instructions.authority_process_flg is 'オーソリ処理フラグ';

comment on column work_regular_authority_instructions.orm_rowid is 'データ行ID';

comment on column work_regular_authority_instructions.created_user is '作成ユーザ';

comment on column work_regular_authority_instructions.created_datetime is '作成日時';

comment on column work_regular_authority_instructions.updated_user is '更新ユーザ';

comment on column work_regular_authority_instructions.updated_datetime is '更新日時';

create unique index work_regular_authority_instructions_ix0
on work_regular_authority_instructions
(orm_rowid)
tablespace ts_order_i01;
