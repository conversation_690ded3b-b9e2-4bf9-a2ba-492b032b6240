create table related_commodity_b
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    link_shop_code                 varchar(16) not null,
    link_commodity_code            varchar(16) not null,
    ranking_score                  numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint related_commodity_b_pk primary key (shop_code, commodity_code, link_shop_code, link_commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table related_commodity_b is '自動リコメンド';

comment on column related_commodity_b.shop_code is 'ショップコード';

comment on column related_commodity_b.commodity_code is '商品コード';

comment on column related_commodity_b.link_shop_code is 'リンクショップコード';

comment on column related_commodity_b.link_commodity_code is 'リンク商品コード';

comment on column related_commodity_b.ranking_score is 'ランキング点数';

comment on column related_commodity_b.orm_rowid is 'データ行ID';

comment on column related_commodity_b.created_user is '作成ユーザ';

comment on column related_commodity_b.created_datetime is '作成日時';

comment on column related_commodity_b.updated_user is '更新ユーザ';

comment on column related_commodity_b.updated_datetime is '更新日時';

create unique index related_commodity_b_ix0
on related_commodity_b
(orm_rowid)
tablespace ts_commodity_i01;
