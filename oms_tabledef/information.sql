create table information
(
    information_no                 numeric(8,0) not null,
    information_type               numeric(1,0) not null,
    information_start_datetime     timestamp(0),
    information_end_datetime       timestamp(0),
    information_content            text not null,
    display_client_type            numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint information_pk primary key (information_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table information is 'お知らせ';

comment on column information.information_no is 'お知らせ番号';

comment on column information.information_type is 'お知らせ区分';

comment on column information.information_start_datetime is 'お知らせ開始日時';

comment on column information.information_end_datetime is 'お知らせ終了日時';

comment on column information.information_content is 'お知らせ内容';

comment on column information.display_client_type is '表示クライアント区分';

comment on column information.orm_rowid is 'データ行ID';

comment on column information.created_user is '作成ユーザ';

comment on column information.created_datetime is '作成日時';

comment on column information.updated_user is '更新ユーザ';

comment on column information.updated_datetime is '更新日時';

create unique index information_ix0
on information
(orm_rowid)
tablespace ts_shop_i01;

create index information_ix1
on information
(information_start_datetime, information_end_datetime)
tablespace ts_shop_i01;
