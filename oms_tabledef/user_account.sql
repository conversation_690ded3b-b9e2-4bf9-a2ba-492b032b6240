create table user_account
(
    user_code                      numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    user_login_id                  varchar(20) not null,
    password                       varchar(128) not null,
    user_name                      varchar(20) not null,
    email                          varchar(256),
    login_error_count              numeric(10,0) not null,
    login_locked_flg               numeric(1,0) not null,
    login_datetime                 timestamp(0),
    memo                           varchar(200),
    password_last_updated_datetime timestamp(0),
    auth_secret_key                varchar(128),
    role_id                        varchar(8) not null,
    login_token                    varchar(100),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint user_account_pk primary key (user_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table user_account is '管理ユーザ';

comment on column user_account.user_code is 'ユーザコード';

comment on column user_account.shop_code is 'ショップコード';

comment on column user_account.user_login_id is 'ユーザログインID';

comment on column user_account.password is 'パスワード';

comment on column user_account.user_name is 'ユーザ名';

comment on column user_account.email is 'メールアドレス';

comment on column user_account.login_error_count is 'ログイン失敗回数';

comment on column user_account.login_locked_flg is 'ログインロックフラグ';

comment on column user_account.login_datetime is 'ログイン日時';

comment on column user_account.memo is 'メモ';

comment on column user_account.password_last_updated_datetime is 'パスワード最終更新日時';

comment on column user_account.auth_secret_key is '認証シークレットキー';

comment on column user_account.role_id is 'ロールID';

comment on column user_account.login_token is 'ログイントークン';

comment on column user_account.orm_rowid is 'データ行ID';

comment on column user_account.created_user is '作成ユーザ';

comment on column user_account.created_datetime is '作成日時';

comment on column user_account.updated_user is '更新ユーザ';

comment on column user_account.updated_datetime is '更新日時';

create unique index user_account_ix0
on user_account
(orm_rowid)
tablespace ts_shop_i01;

create unique index user_account_ix1
on user_account
(shop_code, user_login_id)
tablespace ts_shop_i01;
