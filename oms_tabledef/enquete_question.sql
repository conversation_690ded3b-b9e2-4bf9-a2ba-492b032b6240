create table enquete_question
(
    enquete_code                   varchar(16) not null,
    enquete_question_no            numeric(8,0) not null,
    enquete_question_type          numeric(1,0) not null,
    enquete_question_content       varchar(200) not null,
    display_order                  numeric(8,0) not null,
    necessary_flg                  numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint enquete_question_pk primary key (enquete_code, enquete_question_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table enquete_question is 'アンケート設問';

comment on column enquete_question.enquete_code is 'アンケートコード';

comment on column enquete_question.enquete_question_no is 'アンケート設問番号';

comment on column enquete_question.enquete_question_type is 'アンケート設問区分';

comment on column enquete_question.enquete_question_content is 'アンケート設問内容';

comment on column enquete_question.display_order is '表示順';

comment on column enquete_question.necessary_flg is '必須フラグ';

comment on column enquete_question.orm_rowid is 'データ行ID';

comment on column enquete_question.created_user is '作成ユーザ';

comment on column enquete_question.created_datetime is '作成日時';

comment on column enquete_question.updated_user is '更新ユーザ';

comment on column enquete_question.updated_datetime is '更新日時';

create unique index enquete_question_ix0
on enquete_question
(orm_rowid)
tablespace ts_customer_i01;
