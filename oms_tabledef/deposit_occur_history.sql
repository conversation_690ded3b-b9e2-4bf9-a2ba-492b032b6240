create table deposit_occur_history
(
    deposit_occur_no               numeric(10,0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12) not null,
    deposit_occur_datetime         timestamp(0) not null,
    deposit_occur_reason_kbn       varchar(1) not null,
    deposit_occur_amount           numeric(10,0) not null,
    deposit_occur_henpin_no        varchar(10),
    deposit_occur_order_no         varchar(16),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint deposit_occur_history_pk primary key (deposit_occur_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table deposit_occur_history is '預り金発生履歴';

comment on column deposit_occur_history.deposit_occur_no is '預り金発生履歴番号';

comment on column deposit_occur_history.customer_code is '顧客コード';

comment on column deposit_occur_history.neo_customer_no is '顧客番号';

comment on column deposit_occur_history.deposit_occur_datetime is '預り金発生日時';

comment on column deposit_occur_history.deposit_occur_reason_kbn is '預り金発生理由';

comment on column deposit_occur_history.deposit_occur_amount is '預り金発生金額';

comment on column deposit_occur_history.deposit_occur_henpin_no is '預り金発生元＿返品依頼番号';

comment on column deposit_occur_history.deposit_occur_order_no is '預り金発生元＿受注番号';

comment on column deposit_occur_history.orm_rowid is 'データ行ID';

comment on column deposit_occur_history.created_user is '作成ユーザ';

comment on column deposit_occur_history.created_datetime is '作成日時';

comment on column deposit_occur_history.updated_user is '更新ユーザ';

comment on column deposit_occur_history.updated_datetime is '更新日時';

create unique index deposit_occur_history_ix0
on deposit_occur_history
(orm_rowid)
tablespace ts_customer_i01;

create index deposit_occur_history_ix1
on deposit_occur_history
(deposit_occur_order_no)
tablespace ts_customer_i01;
