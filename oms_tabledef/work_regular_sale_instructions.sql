create table work_regular_sale_instructions
(
    regular_contract_no            varchar(14) not null,
    regular_kaiji                  numeric(5,0) not null,
    regular_contract_detail_no     numeric(3,0) not null,
    calc_base_type                 varchar(2) not null,
    proc_order                     numeric(8,0) not null,
    order_created_flg              numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_regular_sale_instructions_pk primary key (regular_contract_no, regular_kaiji, regular_contract_detail_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_regular_sale_instructions is '定期販売受注指示ワーク';

comment on column work_regular_sale_instructions.regular_contract_no is '定期契約番号';

comment on column work_regular_sale_instructions.regular_kaiji is '定期回次';

comment on column work_regular_sale_instructions.regular_contract_detail_no is '定期契約明細番号';

comment on column work_regular_sale_instructions.calc_base_type is '算出元区分';

comment on column work_regular_sale_instructions.proc_order is '処理順序';

comment on column work_regular_sale_instructions.order_created_flg is '受注作成済フラグ';

comment on column work_regular_sale_instructions.orm_rowid is 'データ行ID';

comment on column work_regular_sale_instructions.created_user is '作成ユーザ';

comment on column work_regular_sale_instructions.created_datetime is '作成日時';

comment on column work_regular_sale_instructions.updated_user is '更新ユーザ';

comment on column work_regular_sale_instructions.updated_datetime is '更新日時';

