create table commodity_detail
(
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    unit_price                     numeric(8,0) not null,
    discount_price                 numeric(8,0),
    reservation_price              numeric(8,0),
    jan_code                       varchar(16),
    standard_detail1_name          varchar(20),
    standard_detail2_name          varchar(20),
    hinban_code                    varchar(24) not null,
    hinban_kind                    varchar(2) not null,
    member_price_applied_flg       numeric(1,0) not null,
    member_price_discount_rate     numeric(3,0) not null,
    member_price                   numeric(8,0) not null,
    air_transport_flg              numeric(1,0) not null,
    commodity_prod_pack_type       varchar(2) not null,
    delivery_note_no_disp_flg      numeric(1,0) not null,
    reduction_point                numeric(10,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint commodity_detail_pk primary key (shop_code, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table commodity_detail is '商品詳細';

comment on column commodity_detail.shop_code is 'ショップコード';

comment on column commodity_detail.sku_code is 'SKUコード';

comment on column commodity_detail.commodity_code is '商品コード';

comment on column commodity_detail.unit_price is '商品単価';

comment on column commodity_detail.discount_price is '特別価格';

comment on column commodity_detail.reservation_price is '予約価格';

comment on column commodity_detail.jan_code is 'JANコード';

comment on column commodity_detail.standard_detail1_name is '規格詳細1名称';

comment on column commodity_detail.standard_detail2_name is '規格詳細2名称';

comment on column commodity_detail.hinban_code is '品番コード';

comment on column commodity_detail.hinban_kind is '品番種別';

comment on column commodity_detail.member_price_applied_flg is '会員価格適用フラグ';

comment on column commodity_detail.member_price_discount_rate is '会員価格値引率';

comment on column commodity_detail.member_price is '会員価格';

comment on column commodity_detail.air_transport_flg is '空輸可否フラグ';

comment on column commodity_detail.commodity_prod_pack_type is '商品包装種類';

comment on column commodity_detail.delivery_note_no_disp_flg is '納品書非表示フラグ';

comment on column commodity_detail.reduction_point is '利用ポイント数';

comment on column commodity_detail.orm_rowid is 'データ行ID';

comment on column commodity_detail.created_user is '作成ユーザ';

comment on column commodity_detail.created_datetime is '作成日時';

comment on column commodity_detail.updated_user is '更新ユーザ';

comment on column commodity_detail.updated_datetime is '更新日時';

create unique index commodity_detail_ix0
on commodity_detail
(orm_rowid)
tablespace ts_commodity_i01;

create unique index commodity_detail_ix1
on commodity_detail
(shop_code, commodity_code, sku_code)
tablespace ts_commodity_i01;

create unique index commodity_detail_ix2
on commodity_detail
(sku_code, shop_code, unit_price, reservation_price, discount_price)
tablespace ts_commodity_i01;

create unique index commodity_detail_lpk
on commodity_detail
(lower(shop_code), lower(sku_code))
tablespace ts_commodity_i01;

create unique index commodity_detail_lix1
on commodity_detail
(lower(shop_code), lower(commodity_code), lower(sku_code))
tablespace ts_commodity_i01;
