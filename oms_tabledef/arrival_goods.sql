create table arrival_goods
(
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    email                          varchar(256) not null,
    customer_code                  varchar(16),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint arrival_goods_pk primary key (shop_code, sku_code, email) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table arrival_goods is '商品入荷お知らせ';

comment on column arrival_goods.shop_code is 'ショップコード';

comment on column arrival_goods.sku_code is 'SKUコード';

comment on column arrival_goods.email is 'メールアドレス';

comment on column arrival_goods.customer_code is '顧客コード';

comment on column arrival_goods.orm_rowid is 'データ行ID';

comment on column arrival_goods.created_user is '作成ユーザ';

comment on column arrival_goods.created_datetime is '作成日時';

comment on column arrival_goods.updated_user is '更新ユーザ';

comment on column arrival_goods.updated_datetime is '更新日時';

create unique index arrival_goods_ix0
on arrival_goods
(orm_rowid)
tablespace ts_commodity_i01;
