create table commission
(
    shop_code                      varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    payment_price_threshold        numeric(8,0) not null,
    payment_commission             numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint commission_pk primary key (shop_code, payment_method_no, payment_price_threshold) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table commission is '支払手数料';

comment on column commission.shop_code is 'ショップコード';

comment on column commission.payment_method_no is '支払方法番号';

comment on column commission.payment_price_threshold is '支払金額閾値';

comment on column commission.payment_commission is '支払手数料';

comment on column commission.orm_rowid is 'データ行ID';

comment on column commission.created_user is '作成ユーザ';

comment on column commission.created_datetime is '作成日時';

comment on column commission.updated_user is '更新ユーザ';

comment on column commission.updated_datetime is '更新日時';

create unique index commission_ix0
on commission
(orm_rowid)
tablespace ts_shop_i01;
