create table category_commodity
(
    shop_code                      varchar(16) not null,
    category_code                  varchar(16) not null,
    commodity_code                 varchar(16) not null,
    category_search_path           varchar(256) not null,
    search_category_code0          varchar(16) not null,
    search_category_code1          varchar(16) not null,
    search_category_code2          varchar(16) not null,
    search_category_code3          varchar(16) not null,
    search_category_code4          varchar(16) not null,
    search_category_code5          varchar(16) not null,
    search_category_code6          varchar(16) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint category_commodity_pk primary key (shop_code, category_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table category_commodity is 'カテゴリ陳列商品';

comment on column category_commodity.shop_code is 'ショップコード';

comment on column category_commodity.category_code is 'カテゴリコード';

comment on column category_commodity.commodity_code is '商品コード';

comment on column category_commodity.category_search_path is 'カテゴリ検索パス';

comment on column category_commodity.search_category_code0 is '検索用カテゴリコード階層0';

comment on column category_commodity.search_category_code1 is '検索用カテゴリコード階層1';

comment on column category_commodity.search_category_code2 is '検索用カテゴリコード階層2';

comment on column category_commodity.search_category_code3 is '検索用カテゴリコード階層3';

comment on column category_commodity.search_category_code4 is '検索用カテゴリコード階層4';

comment on column category_commodity.search_category_code5 is '検索用カテゴリコード階層5';

comment on column category_commodity.search_category_code6 is '検索用カテゴリコード階層6';

comment on column category_commodity.orm_rowid is 'データ行ID';

comment on column category_commodity.created_user is '作成ユーザ';

comment on column category_commodity.created_datetime is '作成日時';

comment on column category_commodity.updated_user is '更新ユーザ';

comment on column category_commodity.updated_datetime is '更新日時';

create unique index category_commodity_ix0
on category_commodity
(orm_rowid)
tablespace ts_commodity_i01;

create unique index category_commodity_ix1
on category_commodity
(shop_code, commodity_code, category_code, category_search_path)
tablespace ts_commodity_i01;

create unique index category_commodity_ix2
on category_commodity
(category_search_path, shop_code, commodity_code, category_code)
tablespace ts_commodity_i01;

create index category_commodity_ix3
on category_commodity
(shop_code, commodity_code)
tablespace ts_commodity_i01;
