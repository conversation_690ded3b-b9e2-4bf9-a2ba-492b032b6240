create table work_order_data_for_output
(
    order_no                       varchar(14),
    seq                            numeric(9,0),
    neo_customer_no                varchar(12),
    client_name                    varchar(100),
    client_postalcode              varchar(7),
    client_address1                varchar(4),
    client_address2                varchar(40),
    client_address3                varchar(40),
    client_address4                varchar(90),
    client_phonenumber             varchar(16),
    destination_name               varchar(100),
    destination_postalcode         varchar(7),
    destination_address1           varchar(4),
    destination_address2           varchar(40),
    destination_address3           varchar(40),
    destination_address4           varchar(90),
    destination_phonenumber        varchar(16),
    delivery_note_address_name     varchar(150),
    delivery_note_address_postalcode varchar(7),
    delivery_note_address1         varchar(4),
    delivery_note_address2         varchar(40),
    delivery_note_address3         varchar(40),
    delivery_note_address4         varchar(90),
    delivery_note_address_phonenumber varchar(16),
    commodity_code                 varchar(16),
    commodity_name                 varchar(100),
    quantity                       numeric(1,0),
    cases_number                   numeric(1,0),
    roses_number                   numeric(5,0),
    application_quantity           numeric(5,0),
    unit_price                     numeric(10,0),
    purchase_amount                numeric(10,0),
    tax                            numeric(12,2),
    total_amount                   numeric(10,0),
    order_datetime                 timestamp(0),
    shiharai_kbn                   numeric(1,0),
    soku_code                      varchar(6),
    slip_kbn                       varchar(10),
    specified_delivery_time        varchar(2),
    desired_delivery_date          timestamp(0),
    remarks                        varchar(40),
    remarks2                       varchar(8),
    remarks3                       varchar(40),
    delivery_note_hidden_flg       numeric(1,0),
    last_point                     numeric(8,0),
    konkai_add_point               numeric(8,0),
    konkai_use_point               numeric(8,0),
    remaining_points               numeric(8,0),
    delivery_kbn                   numeric(2,0),
    temperature_kbn                numeric(1,0),
    mt_service_upperrow            varchar(39),
    mt_service_lowerrow            varchar(44),
    ean128_barcode                 varchar(44),
    regular_msg_kbn                varchar(2),
    event_campaign_kbn             varchar(2),
    order_reception_manager        varchar(20),
    barcode_kbn                    numeric(1,0),
    stage_barcode_one              numeric(8,0),
    stage_barcode_two              numeric(13,0),
    stage_barcode_three            numeric(13,0),
    stage_barcode_four             numeric(13,0),
    delivery_slip_attached_flg     numeric(1,0),
    teiki_next_shipping_date       timestamp(0),
    teiki_next_regular_times       numeric(8,0),
    bundled_pattern_code           varchar(256),
    fps_flg                        varchar(80),
    exclusion_bundled_code         numeric(256,0),
    soryou                         numeric(10,0),
    scheduled_transfer_date        timestamp(0),
    closing_kbn                    numeric(1,0),
    sales_channel_kbn              varchar(2),
    scheduled_point_num            numeric(10,0),
    held_point_num                 numeric(10,0),
    expired_point_num1             numeric(10,0),
    expired_point_num2             numeric(10,0),
    expired_point_num3             numeric(10,0),
    expired_year_month1            numeric(6,0),
    expired_year_month2            numeric(6,0),
    expired_year_month3            numeric(6,0),
    stage_judgment_num             numeric(10,0),
    current_member_stage           numeric(2,0),
    temporary_member_stage         numeric(2,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
)
tablespace ts_order_t01;

comment on table work_order_data_for_output is '受注データ出力ワーク';

comment on column work_order_data_for_output.order_no is '注文番号';

comment on column work_order_data_for_output.seq is 'SEQ';

comment on column work_order_data_for_output.neo_customer_no is '顧客番号';

comment on column work_order_data_for_output.client_name is '依頼主_名称';

comment on column work_order_data_for_output.client_postalcode is '依頼主_郵便番号';

comment on column work_order_data_for_output.client_address1 is '依頼主_住所1';

comment on column work_order_data_for_output.client_address2 is '依頼主_住所2';

comment on column work_order_data_for_output.client_address3 is '依頼主_住所3';

comment on column work_order_data_for_output.client_address4 is '依頼主_住所4';

comment on column work_order_data_for_output.client_phonenumber is '依頼主_電話番号';

comment on column work_order_data_for_output.destination_name is '届け先_名称';

comment on column work_order_data_for_output.destination_postalcode is '届け先_郵便番号';

comment on column work_order_data_for_output.destination_address1 is '届け先_住所1';

comment on column work_order_data_for_output.destination_address2 is '届け先_住所2';

comment on column work_order_data_for_output.destination_address3 is '届け先_住所3';

comment on column work_order_data_for_output.destination_address4 is '届け先_住所4';

comment on column work_order_data_for_output.destination_phonenumber is '届け先_電話番号';

comment on column work_order_data_for_output.delivery_note_address_name is '納品書宛先_名称';

comment on column work_order_data_for_output.delivery_note_address_postalcode is '納品書宛先_郵便番号';

comment on column work_order_data_for_output.delivery_note_address1 is '納品書宛先_住所1';

comment on column work_order_data_for_output.delivery_note_address2 is '納品書宛先_住所2';

comment on column work_order_data_for_output.delivery_note_address3 is '納品書宛先_住所3';

comment on column work_order_data_for_output.delivery_note_address4 is '納品書宛先_住所4';

comment on column work_order_data_for_output.delivery_note_address_phonenumber is '納品書宛先_電話番号';

comment on column work_order_data_for_output.commodity_code is '商品番号';

comment on column work_order_data_for_output.commodity_name is '商品名';

comment on column work_order_data_for_output.quantity is '入数';

comment on column work_order_data_for_output.cases_number is 'ケース数';

comment on column work_order_data_for_output.roses_number is 'バラ数';

comment on column work_order_data_for_output.application_quantity is '申込数量';

comment on column work_order_data_for_output.unit_price is '商品単価';

comment on column work_order_data_for_output.purchase_amount is '御買上額';

comment on column work_order_data_for_output.tax is '税';

comment on column work_order_data_for_output.total_amount is '総合計金額';

comment on column work_order_data_for_output.order_datetime is '受注日';

comment on column work_order_data_for_output.shiharai_kbn is '支払方法区分';

comment on column work_order_data_for_output.soku_code is '倉庫コード';

comment on column work_order_data_for_output.slip_kbn is '伝票区分';

comment on column work_order_data_for_output.specified_delivery_time is '配達指定時間';

comment on column work_order_data_for_output.desired_delivery_date is '配達希望日';

comment on column work_order_data_for_output.remarks is '備考';

comment on column work_order_data_for_output.remarks2 is '備考2';

comment on column work_order_data_for_output.remarks3 is '備考3';

comment on column work_order_data_for_output.delivery_note_hidden_flg is '納品書非表示フラグ';

comment on column work_order_data_for_output.last_point is '前回ポイント';

comment on column work_order_data_for_output.konkai_add_point is '今回加算ポイント';

comment on column work_order_data_for_output.konkai_use_point is '今回使用ポイント';

comment on column work_order_data_for_output.remaining_points is '残ポイント';

comment on column work_order_data_for_output.delivery_kbn is '配送区分';

comment on column work_order_data_for_output.temperature_kbn is '温度区分';

comment on column work_order_data_for_output.mt_service_upperrow is 'MTサービス用上段';

comment on column work_order_data_for_output.mt_service_lowerrow is 'MTサービス用下段';

comment on column work_order_data_for_output.ean128_barcode is 'EAN-128バーコード';

comment on column work_order_data_for_output.regular_msg_kbn is 'レギュラーメッセージ区分';

comment on column work_order_data_for_output.event_campaign_kbn is 'イベントキャンペーン区分';

comment on column work_order_data_for_output.order_reception_manager is '注文受付担当者名';

comment on column work_order_data_for_output.barcode_kbn is 'バーコード区分';

comment on column work_order_data_for_output.stage_barcode_one is '4段バーコード1段目';

comment on column work_order_data_for_output.stage_barcode_two is '4段バーコード2段目';

comment on column work_order_data_for_output.stage_barcode_three is '4段バーコード3段目';

comment on column work_order_data_for_output.stage_barcode_four is '4段バーコード4段目';

comment on column work_order_data_for_output.delivery_slip_attached_flg is '納品書添付フラグ';

comment on column work_order_data_for_output.teiki_next_shipping_date is '定期便次回発送日付';

comment on column work_order_data_for_output.teiki_next_regular_times is '定期便今回定期回次';

comment on column work_order_data_for_output.bundled_pattern_code is '同梱パターンコード';

comment on column work_order_data_for_output.fps_flg is 'FBSランク';

comment on column work_order_data_for_output.exclusion_bundled_code is '除外同梱コード';

comment on column work_order_data_for_output.soryou is '送料';

comment on column work_order_data_for_output.scheduled_transfer_date is '振替予定日';

comment on column work_order_data_for_output.closing_kbn is '締め区分';

comment on column work_order_data_for_output.sales_channel_kbn is '販売経路区分';

comment on column work_order_data_for_output.scheduled_point_num is '付与予定ポイント数';

comment on column work_order_data_for_output.held_point_num is '保有ポイント数';

comment on column work_order_data_for_output.expired_point_num1 is '失効予定ポイント数1';

comment on column work_order_data_for_output.expired_point_num2 is '失効予定ポイント数2';

comment on column work_order_data_for_output.expired_point_num3 is '失効予定ポイント数3';

comment on column work_order_data_for_output.expired_year_month1 is '失効予定年月1';

comment on column work_order_data_for_output.expired_year_month2 is '失効予定年月2';

comment on column work_order_data_for_output.expired_year_month3 is '失効予定年月3';

comment on column work_order_data_for_output.stage_judgment_num is 'ステージ判定ポイント数';

comment on column work_order_data_for_output.current_member_stage is '現会員ステージ';

comment on column work_order_data_for_output.temporary_member_stage is '仮会員ステージ';

comment on column work_order_data_for_output.orm_rowid is 'データ行ID';

comment on column work_order_data_for_output.created_user is '作成ユーザ';

comment on column work_order_data_for_output.created_datetime is '作成日時';

comment on column work_order_data_for_output.updated_user is '更新ユーザ';

comment on column work_order_data_for_output.updated_datetime is '更新日時';

