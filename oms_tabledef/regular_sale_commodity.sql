create table regular_sale_commodity
(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    regular_sale_composition_no    varchar(8) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    display_order                  numeric(8,0) not null,
    regular_sale_commodity_type    varchar(1),
    regular_sale_commodity_point   numeric(3,0),
    difference_price               numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_commodity_pk primary key (shop_code, regular_sale_code, regular_sale_composition_no, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table regular_sale_commodity is '定期便商品構成';

comment on column regular_sale_commodity.shop_code is 'ショップコード';

comment on column regular_sale_commodity.regular_sale_code is '定期便コード';

comment on column regular_sale_commodity.regular_sale_composition_no is '定期便構成グループコード';

comment on column regular_sale_commodity.sku_code is 'SKUコード';

comment on column regular_sale_commodity.commodity_code is '商品コード';

comment on column regular_sale_commodity.display_order is '表示順';

comment on column regular_sale_commodity.regular_sale_commodity_type is '定期便商品構成区分';

comment on column regular_sale_commodity.regular_sale_commodity_point is '定期便商品構成ポイント';

comment on column regular_sale_commodity.difference_price is '増減金額';

comment on column regular_sale_commodity.orm_rowid is 'データ行ID';

comment on column regular_sale_commodity.created_user is '作成ユーザ';

comment on column regular_sale_commodity.created_datetime is '作成日時';

comment on column regular_sale_commodity.updated_user is '更新ユーザ';

comment on column regular_sale_commodity.updated_datetime is '更新日時';

create unique index regular_sale_commodity_ix0
on regular_sale_commodity
(orm_rowid)
tablespace ts_commodity_i01;
