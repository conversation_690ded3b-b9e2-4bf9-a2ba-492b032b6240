create table combine_order
(
    dokon_shiji_code               varchar(5) not null,
    dokon_joken_group_no           numeric(9,0) not null,
    dokon_kind                     varchar(1) not null,
    dokon_joken_seq                numeric(9,0) not null,
    joken_kind                     varchar(2) not null,
    joken                          varchar(16),
    joken_age_from                 numeric(3,0),
    joken_age_to                   numeric(3,0),
    shop_code                      varchar(16),
    sku_code                       varchar(24),
    commodity_code                 varchar(16),
    commodity_name                 varchar(100),
    add_joken                      varchar(2),
    add_joken_from                 numeric(3,0),
    add_joken_to                   numeric(3,0),
    joken_hasso_from               timestamp(0),
    joken_hasso_to                 timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_order_pk primary key (dokon_shiji_code, dokon_joken_group_no, dokon_kind, dokon_joken_seq) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table combine_order is '同梱条件マスタ';

comment on column combine_order.dokon_shiji_code is '同梱指示コード';

comment on column combine_order.dokon_joken_group_no is '同梱条件グループ番号';

comment on column combine_order.dokon_kind is '条件分類';

comment on column combine_order.dokon_joken_seq is '同梱条件連番';

comment on column combine_order.joken_kind is '条件種別';

comment on column combine_order.joken is '条件内容';

comment on column combine_order.joken_age_from is '条件内容(年齢)_FROM';

comment on column combine_order.joken_age_to is '条件内容(年齢)_TO';

comment on column combine_order.shop_code is 'ショップコード';

comment on column combine_order.sku_code is 'SKUコード';

comment on column combine_order.commodity_code is '商品コード';

comment on column combine_order.commodity_name is '商品名称';

comment on column combine_order.add_joken is '追加条件';

comment on column combine_order.add_joken_from is '追加条件(回次)_FROM';

comment on column combine_order.add_joken_to is '追加条件(回次)_TO';

comment on column combine_order.joken_hasso_from is '条件内容(発送日)_FROM';

comment on column combine_order.joken_hasso_to is '条件内容(発送日)_TO';

comment on column combine_order.orm_rowid is 'データ行ID';

comment on column combine_order.created_user is '作成ユーザ';

comment on column combine_order.created_datetime is '作成日時';

comment on column combine_order.updated_user is '更新ユーザ';

comment on column combine_order.updated_datetime is '更新日時';

create unique index combine_order_ix0
on combine_order
(orm_rowid)
tablespace ts_commodity_i01;
