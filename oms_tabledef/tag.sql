create table tag
(
    shop_code                      varchar(16) not null,
    tag_code                       varchar(16) not null,
    tag_name                       varchar(50) not null,
    tag_description                varchar(200),
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint tag_pk primary key (shop_code, tag_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table tag is 'タグ';

comment on column tag.shop_code is 'ショップコード';

comment on column tag.tag_code is 'タグコード';

comment on column tag.tag_name is 'タグ名称';

comment on column tag.tag_description is 'タグ説明';

comment on column tag.display_order is '表示順';

comment on column tag.orm_rowid is 'データ行ID';

comment on column tag.created_user is '作成ユーザ';

comment on column tag.created_datetime is '作成日時';

comment on column tag.updated_user is '更新ユーザ';

comment on column tag.updated_datetime is '更新日時';

create unique index tag_ix0
on tag
(orm_rowid)
tablespace ts_commodity_i01;
