create table reminder
(
    reissuance_key                 varchar(128) not null,
    customer_code                  varchar(16) not null,
    reissued_datetime              timestamp(0) not null,
    mail_send_status               numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint reminder_pk primary key (reissuance_key) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table reminder is 'リマインダ';

comment on column reminder.reissuance_key is '再発行キー';

comment on column reminder.customer_code is '顧客コード';

comment on column reminder.reissued_datetime is '再発行日時';

comment on column reminder.mail_send_status is 'メール送信ステータス';

comment on column reminder.orm_rowid is 'データ行ID';

comment on column reminder.created_user is '作成ユーザ';

comment on column reminder.created_datetime is '作成日時';

comment on column reminder.updated_user is '更新ユーザ';

comment on column reminder.updated_datetime is '更新日時';

create unique index reminder_ix0
on reminder
(orm_rowid)
tablespace ts_customer_i01;
