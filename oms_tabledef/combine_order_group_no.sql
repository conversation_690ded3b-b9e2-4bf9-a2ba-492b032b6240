create table combine_order_group_no
(
    dokon_shiji_code               varchar(5) not null,
    dokon_joken_group_no           numeric(9,0) not null,
    dokon_joken_disp               varchar(500),
    exclude_joken_disp             varchar(500),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_order_group_no_pk primary key (dokon_shiji_code, dokon_joken_group_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table combine_order_group_no is '同梱条件グループ番号マスタ';

comment on column combine_order_group_no.dokon_shiji_code is '同梱指示コード';

comment on column combine_order_group_no.dokon_joken_group_no is '同梱条件グループ番号';

comment on column combine_order_group_no.dokon_joken_disp is '同梱条件（一覧表示用）';

comment on column combine_order_group_no.exclude_joken_disp is '除外条件（一覧表示用）';

comment on column combine_order_group_no.orm_rowid is 'データ行ID';

comment on column combine_order_group_no.created_user is '作成ユーザ';

comment on column combine_order_group_no.created_datetime is '作成日時';

comment on column combine_order_group_no.updated_user is '更新ユーザ';

comment on column combine_order_group_no.updated_datetime is '更新日時';

create unique index combine_order_group_no_ix0
on combine_order_group_no
(orm_rowid)
tablespace ts_commodity_i01;
