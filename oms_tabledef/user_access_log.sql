create table user_access_log
(
    user_access_log_id             numeric(38,0) not null,
    user_code                      numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    user_name                      varchar(20),
    operation_code                 varchar(11) not null,
    access_datetime                timestamp(0) not null,
    ip_address                     varchar(40),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint user_access_log_pk primary key (user_access_log_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table user_access_log is '管理側アクセスログ';

comment on column user_access_log.user_access_log_id is '管理側アクセスログID';

comment on column user_access_log.user_code is 'ユーザコード';

comment on column user_access_log.shop_code is 'ショップコード';

comment on column user_access_log.user_name is '管理ユーザ名称';

comment on column user_access_log.operation_code is 'オペレーションコード';

comment on column user_access_log.access_datetime is 'アクセス日時';

comment on column user_access_log.ip_address is 'IPアドレス';

comment on column user_access_log.orm_rowid is 'データ行ID';

comment on column user_access_log.created_user is '作成ユーザ';

comment on column user_access_log.created_datetime is '作成日時';

comment on column user_access_log.updated_user is '更新ユーザ';

comment on column user_access_log.updated_datetime is '更新日時';

create unique index user_access_log_ix0
on user_access_log
(orm_rowid)
tablespace ts_shop_i01;
