create table commodity_header
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    commodity_name                 varchar(100) not null,
    commodity_type                 numeric(1,0) not null,
    represent_sku_code             varchar(24) not null,
    represent_sku_unit_price       numeric(8,0) not null,
    stock_status_no                numeric(8,0),
    stock_management_type          numeric(1,0) not null,
    age_limit_code                 numeric(8,0) not null,
    commodity_tax_type             numeric(1,0) not null,
    tax_group_code                 varchar(8) not null,
    short_description              varchar(100),
    commodity_search_words         varchar(500),
    prior_printing_description     varchar(1000),
    posterior_printing_description varchar(1000),
    delivery_description           varchar(1000),
    sale_start_datetime            timestamp(0) not null,
    sale_end_datetime              timestamp(0) not null,
    discount_price_start_datetime  timestamp(0),
    discount_price_end_datetime    timestamp(0),
    reservation_start_datetime     timestamp(0) not null,
    reservation_end_datetime       timestamp(0) not null,
    prior_printing_start_date      timestamp(0),
    prior_printing_end_date        timestamp(0),
    posterior_printing_start_date  timestamp(0),
    posterior_printing_end_date    timestamp(0),
    delivery_type_no               numeric(8,0) not null,
    sales_method_type              numeric(1,0) not null,
    manufacturer_model_no          varchar(50),
    link_url                       varchar(256),
    recommend_commodity_rank       numeric(8,0) not null,
    commodity_popular_rank         numeric(8,0) not null,
    commodity_standard1_name       varchar(20),
    commodity_standard2_name       varchar(20),
    commodity_point_rate           numeric(3,0),
    commodity_point_start_datetime timestamp(0),
    commodity_point_end_datetime   timestamp(0),
    sale_flg                       numeric(1,0) not null,
    noshi_effective_flg            numeric(1,0) not null,
    arrival_goods_flg              numeric(1,0) not null,
    oneshot_order_limit            numeric(8,0),
    standard_image_type            numeric(1,0) not null,
    purchasing_confirm_flg_pc      numeric(1,0) not null,
    purchasing_confirm_flg_sp      numeric(1,0) not null,
    commodity_kind                 varchar(2) not null,
    keihi_hurikae_target_flg       numeric(1,0) not null,
    charge_user_code               numeric(38,0),
    commodity_remark               varchar(50),
    channel_cc_sale_flg            numeric(1,0) not null,
    channel_ec_sale_flg            numeric(1,0) not null,
    shipping_charge_target_flg     numeric(1,0) not null,
    first_purchase_limit_flg       numeric(1,0) not null,
    purchase_hold_flg              numeric(1,0) not null,
    commodity_exclude_flg          numeric(1,0) not null,
    commodity_subsubcategory_code  varchar(16),
    pack_calc_pattern              numeric(2,0) not null,
    pad_type                       numeric(2,0) not null,
    fall_down_flg                  numeric(1,0) not null,
    height                         numeric(5,1),
    width                          numeric(5,1),
    deepness                       numeric(5,1),
    weight                         numeric(7,1),
    tracking_out_flg               numeric(1,0) not null,
    mdm_management_code            numeric(20,0),
    commodity_segment              varchar(5),
    business_segment               varchar(5),
    commodity_group                varchar(5),
    commodity_series               varchar(5),
    core_department                varchar(2),
    accounting_pattern_type        varchar(2),
    return_enabled_flg             varchar(1),
    exchange_enabled_flg           varchar(1),
    exterior_box_weight            varchar(10),
    nekoposu_volume_rate           numeric(3,0),
    warehouse_assembly_flg         varchar(1),
    mail_delivery_flg              varchar(1),
    before_renewal_commodity_code  varchar(200),
    preorder_enable_days           numeric(2,0),
    main_product_no                varchar(24),
    product_no                     varchar(24),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint commodity_header_pk primary key (shop_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table commodity_header is '商品ヘッダ';

comment on column commodity_header.shop_code is 'ショップコード';

comment on column commodity_header.commodity_code is '商品コード';

comment on column commodity_header.commodity_name is '商品名称';

comment on column commodity_header.commodity_type is '商品区分';

comment on column commodity_header.represent_sku_code is '代表SKUコード';

comment on column commodity_header.represent_sku_unit_price is '代表SKU単価';

comment on column commodity_header.stock_status_no is '在庫状況番号';

comment on column commodity_header.stock_management_type is '在庫管理区分';

comment on column commodity_header.age_limit_code is '年齢制限コード';

comment on column commodity_header.commodity_tax_type is '商品消費税区分';

comment on column commodity_header.tax_group_code is '消費税グループコード';

comment on column commodity_header.short_description is '概要説明';

comment on column commodity_header.commodity_search_words is '商品検索ワード';

comment on column commodity_header.prior_printing_description is '事前掲載説明';

comment on column commodity_header.posterior_printing_description is '事後掲載説明';

comment on column commodity_header.delivery_description is '納期説明';

comment on column commodity_header.sale_start_datetime is '販売開始日時';

comment on column commodity_header.sale_end_datetime is '販売終了日時';

comment on column commodity_header.discount_price_start_datetime is '特別価格開始日時';

comment on column commodity_header.discount_price_end_datetime is '特別価格終了日時';

comment on column commodity_header.reservation_start_datetime is '予約開始日時';

comment on column commodity_header.reservation_end_datetime is '予約終了日時';

comment on column commodity_header.prior_printing_start_date is '事前掲載開始日時';

comment on column commodity_header.prior_printing_end_date is '事前掲載終了日時';

comment on column commodity_header.posterior_printing_start_date is '事後掲載開始日時';

comment on column commodity_header.posterior_printing_end_date is '事後掲載終了日時';

comment on column commodity_header.delivery_type_no is '配送種別番号';

comment on column commodity_header.sales_method_type is '販売方法区分';

comment on column commodity_header.manufacturer_model_no is 'メーカー型番';

comment on column commodity_header.link_url is 'リンクURL';

comment on column commodity_header.recommend_commodity_rank is 'おすすめ商品順位';

comment on column commodity_header.commodity_popular_rank is '人気順位';

comment on column commodity_header.commodity_standard1_name is '規格名称1';

comment on column commodity_header.commodity_standard2_name is '規格名称2';

comment on column commodity_header.commodity_point_rate is '商品別ポイント付与率';

comment on column commodity_header.commodity_point_start_datetime is '商品別ポイント付与開始日時';

comment on column commodity_header.commodity_point_end_datetime is '商品別ポイント付与終了日時';

comment on column commodity_header.sale_flg is '販売フラグ';

comment on column commodity_header.noshi_effective_flg is '熨斗有効フラグ';

comment on column commodity_header.arrival_goods_flg is '入荷お知らせ可能フラグ';

comment on column commodity_header.oneshot_order_limit is '注文毎注文上限数';

comment on column commodity_header.standard_image_type is '規格画像区分';

comment on column commodity_header.purchasing_confirm_flg_pc is 'PC用購入確認フラグ';

comment on column commodity_header.purchasing_confirm_flg_sp is 'スマートフォン用購入確認フラグ';

comment on column commodity_header.commodity_kind is '商品種別';

comment on column commodity_header.keihi_hurikae_target_flg is '経費振替対象フラグ';

comment on column commodity_header.charge_user_code is '担当ユーザコード';

comment on column commodity_header.commodity_remark is '商品備考';

comment on column commodity_header.channel_cc_sale_flg is 'チャネル別販売フラグ_コールセンター';

comment on column commodity_header.channel_ec_sale_flg is 'チャネル別販売フラグ_Webサイト';

comment on column commodity_header.shipping_charge_target_flg is '送料計算対象フラグ';

comment on column commodity_header.first_purchase_limit_flg is '初回購入限定フラグ';

comment on column commodity_header.purchase_hold_flg is '購入時発送保留フラグ';

comment on column commodity_header.commodity_exclude_flg is '特定商品除外フラグ';

comment on column commodity_header.commodity_subsubcategory_code is '商品小分類';

comment on column commodity_header.pack_calc_pattern is '荷姿計算パターン';

comment on column commodity_header.pad_type is 'パッド使用区分';

comment on column commodity_header.fall_down_flg is '転倒不可フラグ';

comment on column commodity_header.height is '縦';

comment on column commodity_header.width is '横';

comment on column commodity_header.deepness is '奥行き';

comment on column commodity_header.weight is '重量';

comment on column commodity_header.tracking_out_flg is '送り状不要フラグ';

comment on column commodity_header.mdm_management_code is 'MDM統合管理コード';

comment on column commodity_header.commodity_segment is '商品セグメント';

comment on column commodity_header.business_segment is '事業セグメント';

comment on column commodity_header.commodity_group is '商品分類';

comment on column commodity_header.commodity_series is '商品シリーズ';

comment on column commodity_header.core_department is '基幹部門';

comment on column commodity_header.accounting_pattern_type is '会計パターン区分';

comment on column commodity_header.return_enabled_flg is '返品可能フラグ';

comment on column commodity_header.exchange_enabled_flg is '交換可能フラグ';

comment on column commodity_header.exterior_box_weight is '外装箱重量';

comment on column commodity_header.nekoposu_volume_rate is 'ネコポス体積率';

comment on column commodity_header.warehouse_assembly_flg is '倉庫組立セット商品フラグ';

comment on column commodity_header.mail_delivery_flg is 'メール便フラグ';

comment on column commodity_header.before_renewal_commodity_code is 'リニューアル前商品番号';

comment on column commodity_header.preorder_enable_days is '先付け受注可能日数';

comment on column commodity_header.main_product_no is 'オリジナル商品No';

comment on column commodity_header.product_no is '商品No';

comment on column commodity_header.orm_rowid is 'データ行ID';

comment on column commodity_header.created_user is '作成ユーザ';

comment on column commodity_header.created_datetime is '作成日時';

comment on column commodity_header.updated_user is '更新ユーザ';

comment on column commodity_header.updated_datetime is '更新日時';

create unique index commodity_header_ix0
on commodity_header
(orm_rowid)
tablespace ts_commodity_i01;

create index commodity_header_ix1
on commodity_header
(shop_code, sale_end_datetime, sale_start_datetime)
tablespace ts_commodity_i01;

create unique index commodity_header_ix2
on commodity_header
(commodity_name, shop_code, commodity_code)
tablespace ts_commodity_i01;

create unique index commodity_header_ix3
on commodity_header
(shop_code, represent_sku_code)
tablespace ts_commodity_i01;

create index commodity_header_ix4
on commodity_header
(recommend_commodity_rank)
tablespace ts_commodity_i01;

create index commodity_header_ix5
on commodity_header
(commodity_popular_rank)
tablespace ts_commodity_i01;

create unique index commodity_header_ix6
on commodity_header
(shop_code, commodity_code, arrival_goods_flg, delivery_type_no, stock_management_type, sale_flg, reservation_start_datetime, reservation_end_datetime, sale_start_datetime, sale_end_datetime, commodity_name)
tablespace ts_commodity_i01;

create unique index commodity_header_lpk
on commodity_header
(lower(shop_code), lower(commodity_code))
tablespace ts_commodity_i01;

create index commodity_header_ix8
on commodity_header
(stock_management_type)
tablespace ts_commodity_i01;
