create table mail_template_header
(
    shop_code                      varchar(16) not null,
    mail_type                      varchar(2) not null,
    mail_template_no               numeric(8,0) not null,
    mail_content_type              numeric(1,0) not null,
    from_address                   varchar(256) not null,
    cc_address                     varchar(1000),
    bcc_address                    varchar(1000),
    mail_sender_name               varchar(50),
    mail_subject                   varchar(100),
    mail_composition               varchar(150),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint mail_template_header_pk primary key (shop_code, mail_type, mail_template_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table mail_template_header is 'メールテンプレートヘッダ';

comment on column mail_template_header.shop_code is 'ショップコード';

comment on column mail_template_header.mail_type is 'メールタイプ';

comment on column mail_template_header.mail_template_no is 'メールテンプレート番号';

comment on column mail_template_header.mail_content_type is 'メールコンテンツタイプ';

comment on column mail_template_header.from_address is 'FROMアドレス';

comment on column mail_template_header.cc_address is 'CCアドレス';

comment on column mail_template_header.bcc_address is 'BCCアドレス';

comment on column mail_template_header.mail_sender_name is '差出人名';

comment on column mail_template_header.mail_subject is 'メール件名';

comment on column mail_template_header.mail_composition is 'メール構成';

comment on column mail_template_header.orm_rowid is 'データ行ID';

comment on column mail_template_header.created_user is '作成ユーザ';

comment on column mail_template_header.created_datetime is '作成日時';

comment on column mail_template_header.updated_user is '更新ユーザ';

comment on column mail_template_header.updated_datetime is '更新日時';

create unique index mail_template_header_ix0
on mail_template_header
(orm_rowid)
tablespace ts_shop_i01;
