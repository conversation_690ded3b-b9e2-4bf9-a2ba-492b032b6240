create table work_regular_niyose_instructions
(
    regular_contract_no            varchar(14) not null,
    regular_kaiji                  numeric(5,0) not null,
    order_no                       varchar(16),
    niyose_order_no                varchar(16),
    niyose_shipping_method         varchar(2),
    niyose_needed_flg              numeric(1,0),
    niyose_done_flg                numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_regular_niyose_instructions_pk primary key (regular_contract_no, regular_kaiji) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_regular_niyose_instructions is '定期販売受注荷寄せ指示ワーク';

comment on column work_regular_niyose_instructions.regular_contract_no is '定期契約番号';

comment on column work_regular_niyose_instructions.regular_kaiji is '定期回次';

comment on column work_regular_niyose_instructions.order_no is '受注番号';

comment on column work_regular_niyose_instructions.niyose_order_no is '荷寄せ先受注番号';

comment on column work_regular_niyose_instructions.niyose_shipping_method is '荷寄せ後配送方法';

comment on column work_regular_niyose_instructions.niyose_needed_flg is '荷寄せ要フラグ';

comment on column work_regular_niyose_instructions.niyose_done_flg is '荷寄せ済みフラグ';

comment on column work_regular_niyose_instructions.orm_rowid is 'データ行ID';

comment on column work_regular_niyose_instructions.created_user is '作成ユーザ';

comment on column work_regular_niyose_instructions.created_datetime is '作成日時';

comment on column work_regular_niyose_instructions.updated_user is '更新ユーザ';

comment on column work_regular_niyose_instructions.updated_datetime is '更新日時';

create unique index work_regular_niyose_instructions_ix0
on work_regular_niyose_instructions
(orm_rowid)
tablespace ts_order_i01;
