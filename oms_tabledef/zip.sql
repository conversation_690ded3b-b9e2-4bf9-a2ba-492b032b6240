create table zip
(
    postal_code_seq                numeric(8,0) not null,
    postal_code                    varchar(7),
    postal_code_main               varchar(3),
    postal_code_sub                varchar(4),
    prefecture_name                varchar(4),
    city_name                      varchar(40),
    town_name                      varchar(40),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint zip_pk primary key (postal_code_seq) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table zip is '郵便番号';

comment on column zip.postal_code_seq is '郵便番号連番';

comment on column zip.postal_code is '郵便番号';

comment on column zip.postal_code_main is '郵便番号ＭＡＩＮ';

comment on column zip.postal_code_sub is '郵便番号ＳＵＢ';

comment on column zip.prefecture_name is '都道府県名称';

comment on column zip.city_name is '市区町村名';

comment on column zip.town_name is '町域名';

comment on column zip.orm_rowid is 'データ行ID';

comment on column zip.created_user is '作成ユーザ';

comment on column zip.created_datetime is '作成日時';

comment on column zip.updated_user is '更新ユーザ';

comment on column zip.updated_datetime is '更新日時';

create unique index zip_ix0
on zip
(orm_rowid)
tablespace ts_shop_i01;

create index zip_ix1
on zip
(postal_code)
tablespace ts_shop_i01;
