create table campaign
(
    shop_code                      varchar(16) not null,
    campaign_code                  varchar(16) not null,
    campaign_name                  varchar(50) not null,
    campaign_start_date            timestamp(0) not null,
    campaign_end_date              timestamp(0) not null,
    campaign_discount_rate         numeric(3,0) not null,
    memo                           varchar(200),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_pk primary key (shop_code, campaign_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign is 'キャンペーン';

comment on column campaign.shop_code is 'ショップコード';

comment on column campaign.campaign_code is 'キャンペーンコード';

comment on column campaign.campaign_name is 'キャンペーン名称';

comment on column campaign.campaign_start_date is 'キャンペーン開始日';

comment on column campaign.campaign_end_date is 'キャンペーン終了日';

comment on column campaign.campaign_discount_rate is 'キャンペーン値引率';

comment on column campaign.memo is 'メモ';

comment on column campaign.orm_rowid is 'データ行ID';

comment on column campaign.created_user is '作成ユーザ';

comment on column campaign.created_datetime is '作成日時';

comment on column campaign.updated_user is '更新ユーザ';

comment on column campaign.updated_datetime is '更新日時';

create unique index campaign_ix0
on campaign
(orm_rowid)
tablespace ts_commodity_i01;

create index campaign_ix1
on campaign
(campaign_start_date, campaign_end_date)
tablespace ts_commodity_i01;

create index campaign_ix2
on campaign
(campaign_name)
tablespace ts_commodity_i01;

create unique index campaign_lpk
on campaign
(lower(shop_code), lower(campaign_code))
tablespace ts_commodity_i01;
