create table shop
(
    shop_code                      varchar(16) not null,
    open_datetime                  timestamp(0) not null,
    close_datetime                 timestamp(0) not null,
    shop_name                      varchar(30) not null,
    short_shop_name                varchar(10) not null,
    shop_introduced_url            varchar(256),
    email                          varchar(256),
    postal_code                    varchar(7),
    prefecture_code                varchar(2),
    address1                       varchar(4),
    address2                       varchar(50),
    address3                       varchar(255),
    address4                       varchar(100),
    phone_number                   varchar(16),
    person_in_charge               varchar(20),
    ssl_page                       varchar(256),
    shop_type                      numeric(1,0) not null,
    customer_cancelable_flg        numeric(1,0) not null,
    maintenance_flg                numeric(1,0) not null,
    maintenance_url                varchar(256),
    maintenance_excluded_ip        varchar(500),
    invoice_shop_code              varchar(14),
    invoice_shop_name              varchar(30),
    shop_profile                   text,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shop_pk primary key (shop_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table shop is 'ショップ';

comment on column shop.shop_code is 'ショップコード';

comment on column shop.open_datetime is 'ショップ開店日時';

comment on column shop.close_datetime is 'ショップ閉店日時';

comment on column shop.shop_name is 'ショップ名称';

comment on column shop.short_shop_name is 'ショップ略名';

comment on column shop.shop_introduced_url is 'ショップ紹介URL';

comment on column shop.email is 'メールアドレス';

comment on column shop.postal_code is '郵便番号';

comment on column shop.prefecture_code is '都道府県コード';

comment on column shop.address1 is '住所1';

comment on column shop.address2 is '住所2';

comment on column shop.address3 is '住所3';

comment on column shop.address4 is '住所4';

comment on column shop.phone_number is '電話番号';

comment on column shop.person_in_charge is '担当者';

comment on column shop.ssl_page is 'SSLページ';

comment on column shop.shop_type is 'ショップ区分';

comment on column shop.customer_cancelable_flg is '顧客キャンセルフラグ';

comment on column shop.maintenance_flg is 'メンテナンス表示切替フラグ';

comment on column shop.maintenance_url is 'メンテナンスページURL';

comment on column shop.maintenance_excluded_ip is 'メンテナンス切替除外IPアドレス';

comment on column shop.invoice_shop_code is '適格請求書発行事業者番号';

comment on column shop.invoice_shop_name is '適格請求書発行事業者名';

comment on column shop.shop_profile is 'ショッププロフィール';

comment on column shop.orm_rowid is 'データ行ID';

comment on column shop.created_user is '作成ユーザ';

comment on column shop.created_datetime is '作成日時';

comment on column shop.updated_user is '更新ユーザ';

comment on column shop.updated_datetime is '更新日時';

create unique index shop_ix0
on shop
(orm_rowid)
tablespace ts_shop_i01;

create unique index shop_ix1
on shop
(shop_code, close_datetime, open_datetime)
tablespace ts_shop_i01;

create unique index shop_lpk
on shop
(lower(shop_code))
tablespace ts_shop_i01;
