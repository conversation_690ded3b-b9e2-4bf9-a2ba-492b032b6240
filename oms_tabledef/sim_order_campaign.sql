create table sim_order_campaign
(
    sim_order_no                   varchar(16) not null,
    campaign_instructions_code     varchar(16) not null,
    campaign_instructions_name     varchar(50) not null,
    campaign_description           varchar(100),
    campaign_end_date              timestamp(0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint sim_order_campaign_pk primary key (sim_order_no, campaign_instructions_code) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table sim_order_campaign is 'シミュレーション受注キャンペーン適用';

comment on column sim_order_campaign.sim_order_no is 'シミュレーション受注番号';

comment on column sim_order_campaign.campaign_instructions_code is 'キャンペーン設定コード';

comment on column sim_order_campaign.campaign_instructions_name is 'キャンペーン設定名称';

comment on column sim_order_campaign.campaign_description is 'キャンペーン内容';

comment on column sim_order_campaign.campaign_end_date is 'キャンペーン適用終了日';

comment on column sim_order_campaign.orm_rowid is 'データ行ID';

comment on column sim_order_campaign.created_user is '作成ユーザ';

comment on column sim_order_campaign.created_datetime is '作成日時';

comment on column sim_order_campaign.updated_user is '更新ユーザ';

comment on column sim_order_campaign.updated_datetime is '更新日時';

create unique index sim_order_campaign_ix0
on sim_order_campaign
(orm_rowid)
tablespace ts_order_i01;
