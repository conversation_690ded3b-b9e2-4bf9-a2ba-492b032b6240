create table shipping_pending
(
    order_no                       varchar(16) not null,
    shipping_hold_seq              numeric(3,0) not null,
    shipping_hold_reason_kbn       varchar(2) not null,
    shipping_hold_release_kbn      varchar(1) not null,
    shipping_hold_date             timestamp(0),
    shipping_hold_release_date     timestamp(0),
    shipping_hold_release_user_code numeric(38,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_pending_pk primary key (order_no, shipping_hold_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table shipping_pending is '発送保留';

comment on column shipping_pending.order_no is '受注番号';

comment on column shipping_pending.shipping_hold_seq is '発送保留連番';

comment on column shipping_pending.shipping_hold_reason_kbn is '発送保留理由区分';

comment on column shipping_pending.shipping_hold_release_kbn is '保留解除区分';

comment on column shipping_pending.shipping_hold_date is '発送保留日';

comment on column shipping_pending.shipping_hold_release_date is '発送保留解除日';

comment on column shipping_pending.shipping_hold_release_user_code is '発送保留解除者ユーザコード';

comment on column shipping_pending.orm_rowid is 'データ行ID';

comment on column shipping_pending.created_user is '作成ユーザ';

comment on column shipping_pending.created_datetime is '作成日時';

comment on column shipping_pending.updated_user is '更新ユーザ';

comment on column shipping_pending.updated_datetime is '更新日時';

create unique index shipping_pending_ix0
on shipping_pending
(orm_rowid)
tablespace ts_order_i01;

create index shipping_pending_ix1
on shipping_pending
(shipping_hold_date)
tablespace ts_order_i01;

create index shipping_pending_ix2
on shipping_pending
(shipping_hold_release_date)
tablespace ts_order_i01;

create index shipping_pending_ix3
on shipping_pending
(shipping_hold_reason_kbn)
tablespace ts_order_i01;

create index shipping_pending_ix4
on shipping_pending
(shipping_hold_release_kbn)
tablespace ts_order_i01;

create index shipping_pending_ix5
on shipping_pending
(shipping_hold_release_user_code)
tablespace ts_order_i01;
