create table promotion_history
(
    promotion_no                   numeric(9,0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12) not null,
    promotion_date                 timestamp(0) not null,
    promotion_end_date             timestamp(0),
    baitai_code                    varchar(10) not null,
    baitai_name                    varchar(50) not null,
    media_kbn1                     varchar(5) not null,
    promotion_result               varchar(2),
    send_status                    varchar(1),
    order_no                       varchar(16),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint promotion_history_pk primary key (promotion_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table promotion_history is 'プロモーション履歴';

comment on column promotion_history.promotion_no is 'プロモーション番号';

comment on column promotion_history.customer_code is '顧客コード';

comment on column promotion_history.neo_customer_no is '顧客番号';

comment on column promotion_history.promotion_date is 'プロモーション日';

comment on column promotion_history.promotion_end_date is 'プロモーション終了日';

comment on column promotion_history.baitai_code is '媒体コード';

comment on column promotion_history.baitai_name is '媒体名称';

comment on column promotion_history.media_kbn1 is 'メディア区分１';

comment on column promotion_history.promotion_result is 'プロモーション結果';

comment on column promotion_history.send_status is '送付ステータス';

comment on column promotion_history.order_no is '受注番号';

comment on column promotion_history.orm_rowid is 'データ行ID';

comment on column promotion_history.created_user is '作成ユーザ';

comment on column promotion_history.created_datetime is '作成日時';

comment on column promotion_history.updated_user is '更新ユーザ';

comment on column promotion_history.updated_datetime is '更新日時';

create unique index promotion_history_ix0
on promotion_history
(orm_rowid)
tablespace ts_customer_i01;

create index promotion_history_ix1
on promotion_history
(neo_customer_no)
tablespace ts_customer_i01;

create index promotion_history_ix2
on promotion_history
(order_no, customer_code)
tablespace ts_customer_i01;

create index promotion_history_ix3
on promotion_history
(customer_code)
tablespace ts_customer_i01;
