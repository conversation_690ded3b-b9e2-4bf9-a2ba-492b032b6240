create table combine_module
(
    dokon_shiji_code               varchar(5) not null,
    dokon_sizai_seq                numeric(9,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_name                 varchar(100) not null,
    baitai_code                    varchar(10),
    baitai_name                    varchar(50),
    first_dokon_flg                numeric(1,0) not null,
    qt                             numeric(5,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_module_pk primary key (dokon_shiji_code, dokon_sizai_seq, shop_code, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table combine_module is '同梱資材マスタ';

comment on column combine_module.dokon_shiji_code is '同梱指示コード';

comment on column combine_module.dokon_sizai_seq is '同梱資材連番';

comment on column combine_module.shop_code is 'ショップコード';

comment on column combine_module.sku_code is 'SKUコード';

comment on column combine_module.commodity_name is '商品名称';

comment on column combine_module.baitai_code is '媒体コード';

comment on column combine_module.baitai_name is '媒体名称';

comment on column combine_module.first_dokon_flg is '初回同梱フラグ';

comment on column combine_module.qt is '数量';

comment on column combine_module.orm_rowid is 'データ行ID';

comment on column combine_module.created_user is '作成ユーザ';

comment on column combine_module.created_datetime is '作成日時';

comment on column combine_module.updated_user is '更新ユーザ';

comment on column combine_module.updated_datetime is '更新日時';

create unique index combine_module_ix0
on combine_module
(orm_rowid)
tablespace ts_commodity_i01;

create index combine_module_ix1
on combine_module
(shop_code)
tablespace ts_commodity_i01;

create index combine_module_ix2
on combine_module
(sku_code)
tablespace ts_commodity_i01;

create index combine_module_ix3
on combine_module
(first_dokon_flg)
tablespace ts_commodity_i01;

create index combine_module_ix4
on combine_module
(qt)
tablespace ts_commodity_i01;
