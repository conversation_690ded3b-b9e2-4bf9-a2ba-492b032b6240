create table brand
(
    shop_code                      varchar(16) not null,
    brand_code                     varchar(16) not null,
    brand_name                     varchar(50),
    brand_description              varchar(200),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint brand_pk primary key (shop_code, brand_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table brand is 'ブランド';

comment on column brand.shop_code is 'ショップコード';

comment on column brand.brand_code is 'ブランドコード';

comment on column brand.brand_name is 'ブランド名';

comment on column brand.brand_description is 'ブランド説明';

comment on column brand.orm_rowid is 'データ行ID';

comment on column brand.created_user is '作成ユーザ';

comment on column brand.created_datetime is '作成日時';

comment on column brand.updated_user is '更新ユーザ';

comment on column brand.updated_datetime is '更新日時';

create unique index brand_ix0
on brand
(orm_rowid)
tablespace ts_commodity_i01;

create unique index brand_lpk
on brand
(lower(shop_code), lower(brand_code))
tablespace ts_commodity_i01;
