create table order_payment_mng
(
    order_no                       varchar(16) not null,
    order_payment_no               numeric(8,0) not null,
    payment_type                   varchar(2),
    payment_identify_code          text,
    payment_process_type           varchar(2),
    payment_process_status         varchar(2),
    payment_process_price          numeric(8,0),
    payment_process_datetime       timestamp(0),
    payment_process_send_content   text,
    payment_process_receive_content text,
    henpin_request_no              varchar(10),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_payment_mng_pk primary key (order_no, order_payment_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_payment_mng is '受注決済情報管理';

comment on column order_payment_mng.order_no is '受注番号';

comment on column order_payment_mng.order_payment_no is '決済情報連番';

comment on column order_payment_mng.payment_type is '決済種別';

comment on column order_payment_mng.payment_identify_code is '決済識別コード';

comment on column order_payment_mng.payment_process_type is '決済処理区分';

comment on column order_payment_mng.payment_process_status is '決済処理ステータス';

comment on column order_payment_mng.payment_process_price is '決済処理金額';

comment on column order_payment_mng.payment_process_datetime is '決済処理日時';

comment on column order_payment_mng.payment_process_send_content is '決済処理送信内容';

comment on column order_payment_mng.payment_process_receive_content is '決済処理受信内容';

comment on column order_payment_mng.henpin_request_no is '返品依頼番号';

comment on column order_payment_mng.orm_rowid is 'データ行ID';

comment on column order_payment_mng.created_user is '作成ユーザ';

comment on column order_payment_mng.created_datetime is '作成日時';

comment on column order_payment_mng.updated_user is '更新ユーザ';

comment on column order_payment_mng.updated_datetime is '更新日時';

create unique index order_payment_mng_ix0
on order_payment_mng
(orm_rowid)
tablespace ts_order_i01;
