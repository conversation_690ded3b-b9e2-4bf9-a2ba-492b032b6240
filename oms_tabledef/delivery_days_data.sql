create table delivery_days_data
(
    use_map_no                     varchar(4) not null,
    address_zip_code               varchar(7) not null,
    address_province_code          varchar(5) not null,
    yupack_dd_am                   varchar(2) not null,
    yupack_time_am                 varchar(2) not null,
    yupack_dd_pm                   varchar(2) not null,
    yupack_time_pm                 varchar(2) not null,
    chilled_dd_am                  varchar(2) not null,
    chilled_time_am                varchar(2) not null,
    chilled_dd_pm                  varchar(2) not null,
    chilled_time_pm                varchar(2) not null,
    frozen_dd_am                   varchar(2) not null,
    frozen_time_am                 varchar(2) not null,
    frozen_dd_pm                   varchar(2) not null,
    frozen_time_pm                 varchar(2) not null,
    golf_ski_am                    varchar(2) not null,
    golf_ski_pm                    varchar(2) not null,
    exception_area_flg             numeric(1,0) not null,
    cod_flg                        numeric(1,0) not null,
    days_calc_advisability_flg     numeric(1,0) not null,
    memo                           varchar(1),
    start_date                     timestamp(0),
    end_date                       timestamp(0),
    update_kbn                     varchar(1),
    update_date                    timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint delivery_days_data_pk primary key (use_map_no, address_zip_code, address_province_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table delivery_days_data is '送達日数データ';

comment on column delivery_days_data.use_map_no is '利用マップ番号';

comment on column delivery_days_data.address_zip_code is 'あて先郵便番号';

comment on column delivery_days_data.address_province_code is 'あて先行政区コード';

comment on column delivery_days_data.yupack_dd_am is 'ゆうパック送達日数　午前出';

comment on column delivery_days_data.yupack_time_am is 'ゆうパック時間帯　午前出';

comment on column delivery_days_data.yupack_dd_pm is 'ゆうパック送達日数　午後出';

comment on column delivery_days_data.yupack_time_pm is 'ゆうパック時間帯　午後出';

comment on column delivery_days_data.chilled_dd_am is 'チルド送達日数　午前出';

comment on column delivery_days_data.chilled_time_am is 'チルド時間帯　午前出';

comment on column delivery_days_data.chilled_dd_pm is 'チルド送達日数　午後出';

comment on column delivery_days_data.chilled_time_pm is 'チルド時間帯　午後出';

comment on column delivery_days_data.frozen_dd_am is '冷凍送達日数　午前出';

comment on column delivery_days_data.frozen_time_am is '冷凍時間帯　午前出';

comment on column delivery_days_data.frozen_dd_pm is '冷凍送達日数　午後出';

comment on column delivery_days_data.frozen_time_pm is '冷凍時間帯　午後出';

comment on column delivery_days_data.golf_ski_am is 'ゴルフ・スキー午前差出';

comment on column delivery_days_data.golf_ski_pm is 'ゴルフ・スキー午後差出';

comment on column delivery_days_data.exception_area_flg is '例外地域フラグ';

comment on column delivery_days_data.cod_flg is '着払い可否フラグ';

comment on column delivery_days_data.days_calc_advisability_flg is '日数算出不可フラグ';

comment on column delivery_days_data.memo is '特記事項';

comment on column delivery_days_data.start_date is '有効期間　開始日';

comment on column delivery_days_data.end_date is '有効期間　終了日';

comment on column delivery_days_data.update_kbn is '更新区分';

comment on column delivery_days_data.update_date is '更新日';

comment on column delivery_days_data.orm_rowid is 'データ行ID';

comment on column delivery_days_data.created_user is '作成ユーザ';

comment on column delivery_days_data.created_datetime is '作成日時';

comment on column delivery_days_data.updated_user is '更新ユーザ';

comment on column delivery_days_data.updated_datetime is '更新日時';

create unique index delivery_days_data_ix0
on delivery_days_data
(orm_rowid)
tablespace ts_shop_i01;
