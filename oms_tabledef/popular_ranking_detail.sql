create table popular_ranking_detail
(
    popular_ranking_count_id       numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    order_ranking                  numeric(8,0) not null,
    lasttime_order_ranking         numeric(8,0) not null,
    count_ranking                  numeric(8,0) not null,
    lasttime_count_ranking         numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint popular_ranking_detail_pk primary key (popular_ranking_count_id, shop_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table popular_ranking_detail is '人気ランキング詳細';

comment on column popular_ranking_detail.popular_ranking_count_id is '人気ランキング集計ID';

comment on column popular_ranking_detail.shop_code is 'ショップコード';

comment on column popular_ranking_detail.commodity_code is '商品コード';

comment on column popular_ranking_detail.order_ranking is '受注金額ランキング';

comment on column popular_ranking_detail.lasttime_order_ranking is '前回受注金額ランキング';

comment on column popular_ranking_detail.count_ranking is '購入数量ランキング';

comment on column popular_ranking_detail.lasttime_count_ranking is '前回購入数量ランキング';

comment on column popular_ranking_detail.orm_rowid is 'データ行ID';

comment on column popular_ranking_detail.created_user is '作成ユーザ';

comment on column popular_ranking_detail.created_datetime is '作成日時';

comment on column popular_ranking_detail.updated_user is '更新ユーザ';

comment on column popular_ranking_detail.updated_datetime is '更新日時';

create unique index popular_ranking_detail_ix0
on popular_ranking_detail
(orm_rowid)
tablespace ts_commodity_i01;

create index popular_ranking_detail_ix1
on popular_ranking_detail
(shop_code, commodity_code)
tablespace ts_commodity_i01;
