create table combine_history
(
    dokon_rireki_seq               numeric(9,0) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    order_no                       varchar(16) not null,
    dokon_date                     timestamp(0) not null,
    shop_code                      varchar(16),
    sku_code                       varchar(24) not null,
    commodity_name                 varchar(100) not null,
    qt                             numeric(5,0) not null,
    dokon_shiji_code               varchar(5),
    baitai_code                    varchar(10),
    delete_flg                     numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint combine_history_pk primary key (dokon_rireki_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table combine_history is '同梱履歴';

comment on column combine_history.dokon_rireki_seq is '同梱履歴番号';

comment on column combine_history.customer_code is '顧客コード';

comment on column combine_history.neo_customer_no is '顧客番号';

comment on column combine_history.order_no is '受注番号';

comment on column combine_history.dokon_date is '同梱日';

comment on column combine_history.shop_code is 'ショップコード';

comment on column combine_history.sku_code is 'SKUコード';

comment on column combine_history.commodity_name is '商品名称';

comment on column combine_history.qt is '数量';

comment on column combine_history.dokon_shiji_code is '同梱指示コード';

comment on column combine_history.baitai_code is '媒体コード';

comment on column combine_history.delete_flg is '削除フラグ';

comment on column combine_history.orm_rowid is 'データ行ID';

comment on column combine_history.created_user is '作成ユーザ';

comment on column combine_history.created_datetime is '作成日時';

comment on column combine_history.updated_user is '更新ユーザ';

comment on column combine_history.updated_datetime is '更新日時';

create unique index combine_history_ix0
on combine_history
(orm_rowid)
tablespace ts_order_i01;

create index combine_history_ix1
on combine_history
(customer_code)
tablespace ts_order_i01;

create index combine_history_ix2
on combine_history
(order_no)
tablespace ts_order_i01;

create index combine_history_ix3
on combine_history
(shop_code)
tablespace ts_order_i01;

create index combine_history_ix4
on combine_history
(sku_code)
tablespace ts_order_i01;

create index combine_history_ix5
on combine_history
(commodity_name)
tablespace ts_order_i01;

create index combine_history_ix6
on combine_history
(dokon_shiji_code)
tablespace ts_order_i01;
