create table work_shipping_instructions_payment
(
    batch_link_no                  varchar(10),
    order_no                       varchar(16),
    ext_payment_method_type        varchar(2),
    customer_code                  varchar(16),
    shipping_bill_price            numeric(10,0),
    tax                            numeric(8,0),
    last_name                      varchar(20),
    first_name                     varchar(20),
    last_name_kana                 varchar(40),
    first_name_kana                varchar(40),
    postal_code                    varchar(7),
    address1                       varchar(4),
    phone_number                   varchar(16),
    order_recieve_datetime         timestamp(0),
    demand_kbn                     varchar(1),
    payment_request_flg            numeric(1,0) not null,
    payment_request_datetime       timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
)
tablespace ts_order_t01;

comment on table work_shipping_instructions_payment is '発送指示決済対象管理ワーク';

comment on column work_shipping_instructions_payment.batch_link_no is 'バッチ連携番号';

comment on column work_shipping_instructions_payment.order_no is '受注番号';

comment on column work_shipping_instructions_payment.ext_payment_method_type is '支払方法区分（拡張）';

comment on column work_shipping_instructions_payment.customer_code is '顧客コード';

comment on column work_shipping_instructions_payment.shipping_bill_price is '発送時請求額';

comment on column work_shipping_instructions_payment.tax is '消費税額';

comment on column work_shipping_instructions_payment.last_name is '姓';

comment on column work_shipping_instructions_payment.first_name is '名';

comment on column work_shipping_instructions_payment.last_name_kana is '姓かな';

comment on column work_shipping_instructions_payment.first_name_kana is '名かな';

comment on column work_shipping_instructions_payment.postal_code is '郵便番号';

comment on column work_shipping_instructions_payment.address1 is '住所1';

comment on column work_shipping_instructions_payment.phone_number is '電話番号';

comment on column work_shipping_instructions_payment.order_recieve_datetime is '受注受付日時';

comment on column work_shipping_instructions_payment.demand_kbn is '督促区分';

comment on column work_shipping_instructions_payment.payment_request_flg is '決済要求処理フラグ';

comment on column work_shipping_instructions_payment.payment_request_datetime is '決済要求処理日時';

comment on column work_shipping_instructions_payment.orm_rowid is 'データ行ID';

comment on column work_shipping_instructions_payment.created_user is '作成ユーザ';

comment on column work_shipping_instructions_payment.created_datetime is '作成日時';

comment on column work_shipping_instructions_payment.updated_user is '更新ユーザ';

comment on column work_shipping_instructions_payment.updated_datetime is '更新日時';

