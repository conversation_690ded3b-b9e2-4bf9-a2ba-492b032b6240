create table refund
(
    refund_no                      varchar(10) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12) not null,
    refund_amount                  numeric(10,0) not null,
    refund_kbn                     varchar(2) not null,
    refund_regist_datetime         timestamp(0) not null,
    refund_date                    timestamp(0),
    bank_name                      varchar(40),
    bank_branch_name               varchar(40),
    account_type                   varchar(1),
    account_no                     varchar(15),
    account_name                   varchar(30),
    registered_mail_name           varchar(100),
    registered_mail_address        varchar(140),
    refund_memo                    varchar(1000),
    refund_charge_user_code        numeric(38,0),
    change_user_code               numeric(38,0),
    cancel_flg                     numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint refund_pk primary key (refund_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table refund is '返金';

comment on column refund.refund_no is '返金処理番号';

comment on column refund.customer_code is '顧客コード';

comment on column refund.neo_customer_no is '顧客番号';

comment on column refund.refund_amount is '返金額';

comment on column refund.refund_kbn is '返金方法区分';

comment on column refund.refund_regist_datetime is '返金処理日時';

comment on column refund.refund_date is '返金実施日';

comment on column refund.bank_name is '金融機関名称';

comment on column refund.bank_branch_name is '金融機関支店名称';

comment on column refund.account_type is '口座種別';

comment on column refund.account_no is '口座番号';

comment on column refund.account_name is '口座名義（カナ）';

comment on column refund.registered_mail_name is '現金書留送付先名';

comment on column refund.registered_mail_address is '現金書留送付先住所';

comment on column refund.refund_memo is '返金メモ';

comment on column refund.refund_charge_user_code is '返金担当ユーザコード';

comment on column refund.change_user_code is '入力担当ユーザコード';

comment on column refund.cancel_flg is 'キャンセルフラグ';

comment on column refund.orm_rowid is 'データ行ID';

comment on column refund.created_user is '作成ユーザ';

comment on column refund.created_datetime is '作成日時';

comment on column refund.updated_user is '更新ユーザ';

comment on column refund.updated_datetime is '更新日時';

create unique index refund_ix0
on refund
(orm_rowid)
tablespace ts_customer_i01;
