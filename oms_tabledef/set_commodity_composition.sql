create table set_commodity_composition
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    child_commodity_code           varchar(16) not null,
    composition_quantity           numeric(2,0) not null,
    composition_order              numeric(2,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint set_commodity_composition_ix0 unique (orm_rowid) using index
        tablespace ts_commodity_i01,
    constraint set_commodity_composition_pk primary key (shop_code, commodity_code, child_commodity_code, composition_order) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table set_commodity_composition is 'セット商品構成';

comment on column set_commodity_composition.shop_code is 'ショップコード';

comment on column set_commodity_composition.commodity_code is '商品コード';

comment on column set_commodity_composition.child_commodity_code is '子商品コード';

comment on column set_commodity_composition.composition_quantity is '構成数量';

comment on column set_commodity_composition.composition_order is '構成順序';

comment on column set_commodity_composition.orm_rowid is 'データ行ID';

comment on column set_commodity_composition.created_user is '作成ユーザ';

comment on column set_commodity_composition.created_datetime is '作成日時';

comment on column set_commodity_composition.updated_user is '更新ユーザ';

comment on column set_commodity_composition.updated_datetime is '更新日時';

