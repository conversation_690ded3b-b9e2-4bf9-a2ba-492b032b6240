create table delivery_appointed_time
(
    shop_code                      varchar(16) not null,
    delivery_type_no               numeric(8,0) not null,
    delivery_appointed_time_code   varchar(16) not null,
    delivery_appointed_time_name   varchar(15) not null,
    delivery_appointed_time_start  numeric(2,0),
    delivery_appointed_time_end    numeric(2,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint delivery_appointed_time_pk primary key (shop_code, delivery_type_no, delivery_appointed_time_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table delivery_appointed_time is '配送指定時間';

comment on column delivery_appointed_time.shop_code is 'ショップコード';

comment on column delivery_appointed_time.delivery_type_no is '配送種別番号';

comment on column delivery_appointed_time.delivery_appointed_time_code is '配送指定時間コード';

comment on column delivery_appointed_time.delivery_appointed_time_name is '配送指定時間名称';

comment on column delivery_appointed_time.delivery_appointed_time_start is '配送指定時間開始';

comment on column delivery_appointed_time.delivery_appointed_time_end is '配送指定時間終了';

comment on column delivery_appointed_time.orm_rowid is 'データ行ID';

comment on column delivery_appointed_time.created_user is '作成ユーザ';

comment on column delivery_appointed_time.created_datetime is '作成日時';

comment on column delivery_appointed_time.updated_user is '更新ユーザ';

comment on column delivery_appointed_time.updated_datetime is '更新日時';

create unique index delivery_appointed_time_ix0
on delivery_appointed_time
(orm_rowid)
tablespace ts_shop_i01;
