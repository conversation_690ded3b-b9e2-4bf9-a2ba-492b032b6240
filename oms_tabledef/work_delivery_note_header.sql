create table work_delivery_note_header
(
    slip_ino                       varchar(14) not null,
    order_no                       varchar(14),
    memb_no                        varchar(8),
    cust_name                      varchar(100),
    order_date                     timestamp(0),
    total_weight                   numeric(10,0),
    work_title                     varchar(50),
    work_sign_text                 varchar(50),
    order_id                       varchar(10),
    out_plan_date                  timestamp(0),
    add_dely_day                   numeric(2,0),
    picking_seq                    varchar(12),
    serial_no                      varchar(5),
    dely_code                      varchar(1),
    tel                            varchar(15),
    insert_date                    timestamp(0),
    insert_id                      varchar(10),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_delivery_note_header_pk primary key (slip_ino) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_delivery_note_header is '納品書ヘッダワーク';

comment on column work_delivery_note_header.slip_ino is '基幹出荷指示番号';

comment on column work_delivery_note_header.order_no is '注文番号';

comment on column work_delivery_note_header.memb_no is '会員番号';

comment on column work_delivery_note_header.cust_name is '顧客名';

comment on column work_delivery_note_header.order_date is '注文日';

comment on column work_delivery_note_header.total_weight is '重量';

comment on column work_delivery_note_header.work_title is 'メール便、定期便等のタイトル';

comment on column work_delivery_note_header.work_sign_text is '納品書不要時のタイトル';

comment on column work_delivery_note_header.order_id is '注文者ID';

comment on column work_delivery_note_header.out_plan_date is '到着予定日';

comment on column work_delivery_note_header.add_dely_day is 'リードタイム加算日付';

comment on column work_delivery_note_header.picking_seq is 'ピンキング指示番号';

comment on column work_delivery_note_header.serial_no is 'シリアル番号';

comment on column work_delivery_note_header.dely_code is '配送業者コード';

comment on column work_delivery_note_header.tel is '電話番号';

comment on column work_delivery_note_header.insert_date is 'insert_date';

comment on column work_delivery_note_header.insert_id is 'insert_id';

comment on column work_delivery_note_header.orm_rowid is 'データ行ID';

comment on column work_delivery_note_header.created_user is '作成ユーザ';

comment on column work_delivery_note_header.created_datetime is '作成日時';

comment on column work_delivery_note_header.updated_user is '更新ユーザ';

comment on column work_delivery_note_header.updated_datetime is '更新日時';

create unique index work_delivery_note_header_ix0
on work_delivery_note_header
(orm_rowid)
tablespace ts_order_i01;
