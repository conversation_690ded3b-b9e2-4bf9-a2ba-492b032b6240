create table campaign_instructions_commodity
(
    campaign_instructions_code     varchar(16) not null,
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    joken_type                     varchar(1) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint campaign_instructions_commodity_pk primary key (campaign_instructions_code, shop_code, commodity_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table campaign_instructions_commodity is 'キャンペーン設定商品';

comment on column campaign_instructions_commodity.campaign_instructions_code is 'キャンペーン設定コード';

comment on column campaign_instructions_commodity.shop_code is 'ショップコード';

comment on column campaign_instructions_commodity.commodity_code is '商品コード';

comment on column campaign_instructions_commodity.joken_type is '条件分類';

comment on column campaign_instructions_commodity.orm_rowid is 'データ行ID';

comment on column campaign_instructions_commodity.created_user is '作成ユーザ';

comment on column campaign_instructions_commodity.created_datetime is '作成日時';

comment on column campaign_instructions_commodity.updated_user is '更新ユーザ';

comment on column campaign_instructions_commodity.updated_datetime is '更新日時';

create unique index campaign_instructions_commodity_ix0
on campaign_instructions_commodity
(orm_rowid)
tablespace ts_commodity_i01;
