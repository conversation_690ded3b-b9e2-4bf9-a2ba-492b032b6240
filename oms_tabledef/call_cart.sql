create table call_cart
(
    cart_no                        varchar(20) not null,
    cart_detail_no                 numeric(4,0) not null,
    allocated_warehouse_code       varchar(6) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    baitai_code                    varchar(10) not null,
    commodity_code                 varchar(16) not null,
    hinban_code                    varchar(24) not null,
    temporary_allocated_quantity   numeric(8,0) not null,
    temporary_reserved_quantity    numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint call_cart_pk primary key (cart_no, cart_detail_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table call_cart is 'コールセンターカート';

comment on column call_cart.cart_no is 'カート番号';

comment on column call_cart.cart_detail_no is 'カート明細番号';

comment on column call_cart.allocated_warehouse_code is '引当倉庫コード';

comment on column call_cart.shop_code is 'ショップコード';

comment on column call_cart.sku_code is 'SKUコード';

comment on column call_cart.baitai_code is '媒体コード';

comment on column call_cart.commodity_code is '商品コード';

comment on column call_cart.hinban_code is '品番コード';

comment on column call_cart.temporary_allocated_quantity is '仮引当数量';

comment on column call_cart.temporary_reserved_quantity is '仮予約数量';

comment on column call_cart.orm_rowid is 'データ行ID';

comment on column call_cart.created_user is '作成ユーザ';

comment on column call_cart.created_datetime is '作成日時';

comment on column call_cart.updated_user is '更新ユーザ';

comment on column call_cart.updated_datetime is '更新日時';

create unique index call_cart_ix0
on call_cart
(orm_rowid)
tablespace ts_order_i01;
