create table wms_returns_history
(
    wms_henpin_rireki_no           varchar(10) not null,
    wms_henpin_date                timestamp(0) not null,
    wms_henpin_match_flg           numeric(1,0) not null,
    wms_henpin_match_kbn           varchar(1) not null,
    wms_nyuko_date                 timestamp(0) not null,
    wms_mng_no                     varchar(30) not null,
    wms_customer_no                varchar(12),
    wms_customer_name              varchar(140),
    wms_tel_no                     varchar(16),
    wms_address                    varchar(1000),
    wms_order_no                   varchar(16),
    wms_commodity_code             varchar(16),
    wms_commodity_name             varchar(300),
    wms_hinban_code                varchar(24),
    wms_hinban_name                varchar(50),
    wms_henpin_quantity            numeric(5,0),
    wms_lot_no                     varchar(90),
    wms_letter_flg                 numeric(1,0) not null,
    wms_henpin_reason_kbn          varchar(2) not null,
    wms_ryohin_kbn                 varchar(2) not null,
    wms_henpin_note                varchar(1000),
    order_no                       varchar(16),
    customer_code                  varchar(16),
    relation_flg                   numeric(1,0) not null,
    sales_recording_date           timestamp(0),
    sales_recording_flg            numeric(1,0) not null,
    no_display_flg                 numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint wms_returns_history_pk primary key (wms_henpin_rireki_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table wms_returns_history is 'WMS返品履歴';

comment on column wms_returns_history.wms_henpin_rireki_no is 'WMS返品履歴番号';

comment on column wms_returns_history.wms_henpin_date is 'WMS返品情報取込日';

comment on column wms_returns_history.wms_henpin_match_flg is 'WMS返品情報突合結果フラグ';

comment on column wms_returns_history.wms_henpin_match_kbn is 'WMS返品情報突合NG理由区分';

comment on column wms_returns_history.wms_nyuko_date is 'WMS入庫日';

comment on column wms_returns_history.wms_mng_no is 'WMS管理番号';

comment on column wms_returns_history.wms_customer_no is 'WMS顧客番号';

comment on column wms_returns_history.wms_customer_name is 'WMS顧客名';

comment on column wms_returns_history.wms_tel_no is 'WMS電話番号';

comment on column wms_returns_history.wms_address is 'WMS住所';

comment on column wms_returns_history.wms_order_no is 'WMS受注番号';

comment on column wms_returns_history.wms_commodity_code is 'WMS商品コード';

comment on column wms_returns_history.wms_commodity_name is 'WMS商品名称';

comment on column wms_returns_history.wms_hinban_code is 'WMS品番コード';

comment on column wms_returns_history.wms_hinban_name is 'WMS品番名称';

comment on column wms_returns_history.wms_henpin_quantity is 'WMS返品数量';

comment on column wms_returns_history.wms_lot_no is 'WMSロット番号';

comment on column wms_returns_history.wms_letter_flg is 'WMS手紙有無フラグ';

comment on column wms_returns_history.wms_henpin_reason_kbn is 'WMS返品理由';

comment on column wms_returns_history.wms_ryohin_kbn is 'WMS良品区分';

comment on column wms_returns_history.wms_henpin_note is 'WMS返品情報備考';

comment on column wms_returns_history.order_no is '受注番号';

comment on column wms_returns_history.customer_code is '顧客コード';

comment on column wms_returns_history.relation_flg is '紐付フラグ';

comment on column wms_returns_history.sales_recording_date is '売上計上日';

comment on column wms_returns_history.sales_recording_flg is '売上計上フラグ';

comment on column wms_returns_history.no_display_flg is '非表示フラグ';

comment on column wms_returns_history.orm_rowid is 'データ行ID';

comment on column wms_returns_history.created_user is '作成ユーザ';

comment on column wms_returns_history.created_datetime is '作成日時';

comment on column wms_returns_history.updated_user is '更新ユーザ';

comment on column wms_returns_history.updated_datetime is '更新日時';

create unique index wms_returns_history_ix0
on wms_returns_history
(orm_rowid)
tablespace ts_order_i01;

create index wms_returns_history_ix1
on wms_returns_history
(wms_order_no)
tablespace ts_order_i01;
