create table order_campaign_use_amount
(
    order_no                       varchar(16) not null,
    tax_group_code                 varchar(8) not null,
    tax_no                         numeric(3,0) not null,
    use_code_type                  varchar(1) not null,
    use_code                       varchar(16) not null,
    use_amount                     numeric(8,0) not null,
    tax_rate                       numeric(3,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint order_campaign_use_amount_pk primary key (order_no, tax_group_code, tax_no, use_code_type, use_code) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table order_campaign_use_amount is '受注キャンペーンクーポン利用額';

comment on column order_campaign_use_amount.order_no is '受注番号';

comment on column order_campaign_use_amount.tax_group_code is '消費税グループコード';

comment on column order_campaign_use_amount.tax_no is '消費税番号';

comment on column order_campaign_use_amount.use_code_type is '利用コード種別';

comment on column order_campaign_use_amount.use_code is '利用コード';

comment on column order_campaign_use_amount.use_amount is '利用額';

comment on column order_campaign_use_amount.tax_rate is '消費税率';

comment on column order_campaign_use_amount.orm_rowid is 'データ行ID';

comment on column order_campaign_use_amount.created_user is '作成ユーザ';

comment on column order_campaign_use_amount.created_datetime is '作成日時';

comment on column order_campaign_use_amount.updated_user is '更新ユーザ';

comment on column order_campaign_use_amount.updated_datetime is '更新日時';

create unique index order_campaign_use_amount_ix0
on order_campaign_use_amount
(orm_rowid)
tablespace ts_order_i01;
