create table returns_header
(
    henpin_request_no              varchar(10) not null,
    order_no                       varchar(16) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    henpin_confirm_status          varchar(1) not null,
    henpin_request_datetime        timestamp(0) not null,
    henpin_confirm_datetime        timestamp(0),
    henpin_recieve_user_code       numeric(38,0),
    henpin_change_kbn              varchar(1) not null,
    henpin_reason_kbn              varchar(2) not null,
    henpin_bill_cancel_flg         numeric(1,0) not null,
    henpin_shipping_flg            numeric(1,0) not null,
    henpin_souko_kbn               varchar(1) not null,
    total_shipping_amount          numeric(10,0) not null,
    adjustment_amount              numeric(10,0) not null,
    bill_price                     numeric(10,0) not null,
    bill_price_bf_henpin           numeric(10,0) not null,
    appropriation_amount_bf_henpin numeric(10,0) not null,
    deposit_occur_amount           numeric(10,0) not null,
    delivery_note_republish_flg    numeric(1,0) not null,
    credit_cancel_flg              numeric(1,0) not null,
    request_af_henpin_confirm_kbn  varchar(1) not null,
    amzn_refund_flg                numeric(1,0) not null,
    amzn_refund_id                 text,
    amzn_refund_status             varchar(2),
    amzn_refund_initiated_datetime timestamp(0),
    amzn_refunded_datetime         timestamp(0),
    refund_amount                  numeric(10,0),
    bank_name                      varchar(40),
    bank_branch_name               varchar(40),
    account_type                   varchar(1),
    account_no                     varchar(15),
    account_name                   varchar(30),
    registered_mail_address        varchar(140),
    registered_mail_name           varchar(100),
    registered_remarks             varchar(1000),
    appropriation_date             timestamp(0),
    appropriation_amount           numeric(10,0),
    wms_contact_flg                numeric(1,0) not null,
    wms_auto_confirm_flg           numeric(1,0) not null,
    sales_recording_date           timestamp(0),
    sales_recording_flg            numeric(1,0) not null,
    inquiry_kanri_no               varchar(10),
    cancel_flg                     numeric(1,0) not null,
    change_user_code               numeric(38,0),
    before_grant_point_total       numeric(10,0),
    before_reduction_point_total   numeric(10,0),
    after_grant_plan_point_prod    numeric(10,0),
    after_grant_plan_point_other   numeric(10,0),
    after_grant_plan_point_total   numeric(10,0),
    after_grant_point_prod         numeric(10,0),
    after_grant_point_other        numeric(10,0),
    after_grant_point_total        numeric(10,0),
    after_reduction_plan_point_total numeric(10,0),
    after_reduction_point_total    numeric(10,0),
    before_coupon_used_amount      numeric(8,0),
    after_coupon_used_amount       numeric(8,0),
    campaign_henpin_price          numeric(8,0),
    coupon_henpin_price            numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint returns_header_pk primary key (henpin_request_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table returns_header is '返品ヘッダ';

comment on column returns_header.henpin_request_no is '返品依頼番号';

comment on column returns_header.order_no is '受注番号';

comment on column returns_header.customer_code is '顧客コード';

comment on column returns_header.neo_customer_no is '顧客番号';

comment on column returns_header.henpin_confirm_status is '返品確認ステータス';

comment on column returns_header.henpin_request_datetime is '返品依頼登録日時';

comment on column returns_header.henpin_confirm_datetime is '返品確認日時';

comment on column returns_header.henpin_recieve_user_code is '返品受付担当者ユーザコード';

comment on column returns_header.henpin_change_kbn is '返品交換区分';

comment on column returns_header.henpin_reason_kbn is '返品理由区分';

comment on column returns_header.henpin_bill_cancel_flg is '返品確認後請求取消フラグ';

comment on column returns_header.henpin_shipping_flg is '返品確認後発送フラグ';

comment on column returns_header.henpin_souko_kbn is '返品入庫倉庫';

comment on column returns_header.total_shipping_amount is '送料合計額';

comment on column returns_header.adjustment_amount is '調整金額';

comment on column returns_header.bill_price is '請求額';

comment on column returns_header.bill_price_bf_henpin is '返品前請求額';

comment on column returns_header.appropriation_amount_bf_henpin is '返品前充当額';

comment on column returns_header.deposit_occur_amount is '預り金発生金額';

comment on column returns_header.delivery_note_republish_flg is '納品書再発行フラグ';

comment on column returns_header.credit_cancel_flg is 'クレジット決済取消フラグ';

comment on column returns_header.request_af_henpin_confirm_kbn is '返品確認後依頼区分';

comment on column returns_header.amzn_refund_flg is 'AmazonPay返金フラグ';

comment on column returns_header.amzn_refund_id is 'AmazonPay返金ID';

comment on column returns_header.amzn_refund_status is 'AmazonPay返金ステータス';

comment on column returns_header.amzn_refund_initiated_datetime is 'AmazonPay返金処理日時';

comment on column returns_header.amzn_refunded_datetime is 'AmazonPay返金確定日時';

comment on column returns_header.refund_amount is '返金メモ＿返金額';

comment on column returns_header.bank_name is '返金メモ＿金融機関名称';

comment on column returns_header.bank_branch_name is '返金メモ＿支店名称';

comment on column returns_header.account_type is '返金メモ＿口座種別';

comment on column returns_header.account_no is '返金メモ＿口座番号';

comment on column returns_header.account_name is '返金メモ＿口座名義（カナ）';

comment on column returns_header.registered_mail_address is '返金メモ＿現金書留送付先住所';

comment on column returns_header.registered_mail_name is '返金メモ＿現金書留送付先名';

comment on column returns_header.registered_remarks is '返金メモ＿備考';

comment on column returns_header.appropriation_date is '充当メモ＿充当先（日付）';

comment on column returns_header.appropriation_amount is '充当メモ＿充当先（金額）';

comment on column returns_header.wms_contact_flg is '連絡有フラグ';

comment on column returns_header.wms_auto_confirm_flg is '自動確認フラグ';

comment on column returns_header.sales_recording_date is '売上計上日';

comment on column returns_header.sales_recording_flg is '売上計上フラグ';

comment on column returns_header.inquiry_kanri_no is '問い合わせ管理番号';

comment on column returns_header.cancel_flg is 'キャンセルフラグ';

comment on column returns_header.change_user_code is '入力担当ユーザコード';

comment on column returns_header.before_grant_point_total is '返品前付与ポイント数';

comment on column returns_header.before_reduction_point_total is '返品前利用ポイント数';

comment on column returns_header.after_grant_plan_point_prod is '返品後付与予定ポイント数（商品）';

comment on column returns_header.after_grant_plan_point_other is '返品後付与予定ポイント数（その他）';

comment on column returns_header.after_grant_plan_point_total is '返品後付与予定ポイント数（合計）';

comment on column returns_header.after_grant_point_prod is '返品後付与ポイント数（商品）';

comment on column returns_header.after_grant_point_other is '返品後付与ポイント数（その他）';

comment on column returns_header.after_grant_point_total is '返品後付与ポイント数（合計）';

comment on column returns_header.after_reduction_plan_point_total is '返品後利用予定ポイント数（合計）';

comment on column returns_header.after_reduction_point_total is '返品後利用ポイント数（合計）';

comment on column returns_header.before_coupon_used_amount is '返品前クーポン利用額';

comment on column returns_header.after_coupon_used_amount is '返品後クーポン利用額';

comment on column returns_header.campaign_henpin_price is 'キャンペーン返品額';

comment on column returns_header.coupon_henpin_price is 'クーポン返品額';

comment on column returns_header.orm_rowid is 'データ行ID';

comment on column returns_header.created_user is '作成ユーザ';

comment on column returns_header.created_datetime is '作成日時';

comment on column returns_header.updated_user is '更新ユーザ';

comment on column returns_header.updated_datetime is '更新日時';

create unique index returns_header_ix0
on returns_header
(orm_rowid)
tablespace ts_order_i01;

create index returns_header_ix1
on returns_header
(order_no)
tablespace ts_order_i01;
