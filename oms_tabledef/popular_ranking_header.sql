create table popular_ranking_header
(
    popular_ranking_count_id       numeric(38,0) not null,
    count_datetime                 timestamp(0),
    count_term_start_date          timestamp(0),
    count_term_end_date            timestamp(0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint popular_ranking_header_pk primary key (popular_ranking_count_id) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table popular_ranking_header is '人気ランキングヘッダ';

comment on column popular_ranking_header.popular_ranking_count_id is '人気ランキング集計ID';

comment on column popular_ranking_header.count_datetime is '集計日時';

comment on column popular_ranking_header.count_term_start_date is '集計期間開始日';

comment on column popular_ranking_header.count_term_end_date is '集計期間終了日';

comment on column popular_ranking_header.orm_rowid is 'データ行ID';

comment on column popular_ranking_header.created_user is '作成ユーザ';

comment on column popular_ranking_header.created_datetime is '作成日時';

comment on column popular_ranking_header.updated_user is '更新ユーザ';

comment on column popular_ranking_header.updated_datetime is '更新日時';

create unique index popular_ranking_header_ix0
on popular_ranking_header
(orm_rowid)
tablespace ts_commodity_i01;
