create table customer_group
(
    customer_group_code            varchar(16) not null,
    customer_group_name            varchar(40) not null,
    customer_group_point_rate      numeric(3,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint customer_group_pk primary key (customer_group_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table customer_group is '顧客グループ';

comment on column customer_group.customer_group_code is '顧客グループコード';

comment on column customer_group.customer_group_name is '顧客グループ名称';

comment on column customer_group.customer_group_point_rate is '顧客グループポイント率';

comment on column customer_group.orm_rowid is 'データ行ID';

comment on column customer_group.created_user is '作成ユーザ';

comment on column customer_group.created_datetime is '作成日時';

comment on column customer_group.updated_user is '更新ユーザ';

comment on column customer_group.updated_datetime is '更新日時';

create unique index customer_group_ix0
on customer_group
(orm_rowid)
tablespace ts_customer_i01;
