create table regular_sale_composition
(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    regular_sale_composition_no    varchar(8) not null,
    regular_sale_composition_name  varchar(50),
    regular_order_count_min_limit  numeric(5,0) not null,
    regular_order_count_max_limit  numeric(5,0),
    regular_order_count_interval   numeric(5,0) not null,
    retail_price                   numeric(8,0) not null,
    regular_sale_commodity_point   numeric(3,0),
    display_order                  numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_composition_pk primary key (shop_code, regular_sale_code, regular_sale_composition_no) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table regular_sale_composition is '定期便構成情報';

comment on column regular_sale_composition.shop_code is 'ショップコード';

comment on column regular_sale_composition.regular_sale_code is '定期便コード';

comment on column regular_sale_composition.regular_sale_composition_no is '定期便構成グループコード';

comment on column regular_sale_composition.regular_sale_composition_name is '定期便構成名称';

comment on column regular_sale_composition.regular_order_count_min_limit is '定期回次下限';

comment on column regular_sale_composition.regular_order_count_max_limit is '定期回次上限';

comment on column regular_sale_composition.regular_order_count_interval is '定期回次間隔';

comment on column regular_sale_composition.retail_price is '販売価格';

comment on column regular_sale_composition.regular_sale_commodity_point is '定期便商品構成ポイント';

comment on column regular_sale_composition.display_order is '表示順';

comment on column regular_sale_composition.orm_rowid is 'データ行ID';

comment on column regular_sale_composition.created_user is '作成ユーザ';

comment on column regular_sale_composition.created_datetime is '作成日時';

comment on column regular_sale_composition.updated_user is '更新ユーザ';

comment on column regular_sale_composition.updated_datetime is '更新日時';

create unique index regular_sale_composition_ix0
on regular_sale_composition
(orm_rowid)
tablespace ts_commodity_i01;
