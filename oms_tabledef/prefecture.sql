create table prefecture
(
    prefecture_code                varchar(2) not null,
    prefecture_name                varchar(4) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint prefecture_pk primary key (prefecture_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table prefecture is '都道府県';

comment on column prefecture.prefecture_code is '都道府県コード';

comment on column prefecture.prefecture_name is '都道府県名称';

comment on column prefecture.orm_rowid is 'データ行ID';

comment on column prefecture.created_user is '作成ユーザ';

comment on column prefecture.created_datetime is '作成日時';

comment on column prefecture.updated_user is '更新ユーザ';

comment on column prefecture.updated_datetime is '更新日時';

create unique index prefecture_ix0
on prefecture
(orm_rowid)
tablespace ts_shop_i01;
