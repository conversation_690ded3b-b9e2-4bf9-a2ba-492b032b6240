create table general_name
(
    shop_code                      varchar(16) not null,
    general_name_group_code        varchar(30) not null,
    general_name_code              varchar(16) not null,
    general_name                   varchar(100) not null,
    display_flg                    numeric(1,0) not null,
    display_order                  numeric(8,0) not null,
    string01                       varchar(1000),
    string02                       varchar(1000),
    string03                       varchar(1000),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint general_name_pk primary key (shop_code, general_name_group_code, general_name_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table general_name is '汎用名称';

comment on column general_name.shop_code is 'ショップコード';

comment on column general_name.general_name_group_code is '汎用グループコード';

comment on column general_name.general_name_code is '汎用名称コード';

comment on column general_name.general_name is '汎用名称';

comment on column general_name.display_flg is '表示フラグ';

comment on column general_name.display_order is '表示順';

comment on column general_name.string01 is '文字列01';

comment on column general_name.string02 is '文字列02';

comment on column general_name.string03 is '文字列03';

comment on column general_name.orm_rowid is 'データ行ID';

comment on column general_name.created_user is '作成ユーザ';

comment on column general_name.created_datetime is '作成日時';

comment on column general_name.updated_user is '更新ユーザ';

comment on column general_name.updated_datetime is '更新日時';

create unique index general_name_ix0
on general_name
(orm_rowid)
tablespace ts_shop_i01;
