create table payment_method_logo
(
    shop_code                      varchar(16) not null,
    payment_method_no              numeric(8,0) not null,
    payment_method_type            varchar(2) not null,
    payment_method_logo_type       varchar(2) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint payment_method_logo_pk primary key (shop_code, payment_method_no, payment_method_type, payment_method_logo_type) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table payment_method_logo is '支払方法ロゴ';

comment on column payment_method_logo.shop_code is 'ショップコード';

comment on column payment_method_logo.payment_method_no is '支払方法番号';

comment on column payment_method_logo.payment_method_type is '支払方法区分';

comment on column payment_method_logo.payment_method_logo_type is '支払方法ロゴ区分';

comment on column payment_method_logo.orm_rowid is 'データ行ID';

comment on column payment_method_logo.created_user is '作成ユーザ';

comment on column payment_method_logo.created_datetime is '作成日時';

comment on column payment_method_logo.updated_user is '更新ユーザ';

comment on column payment_method_logo.updated_datetime is '更新日時';

create unique index payment_method_logo_ix0
on payment_method_logo
(orm_rowid)
tablespace ts_shop_i01;
