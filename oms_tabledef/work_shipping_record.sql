create table work_shipping_record
(
    shipping_jisseki_seq           numeric(15,0) not null,
    order_no                       varchar(16) not null,
    delivery_address_tel           varchar(16) not null,
    delivery_address_name          varchar(200),
    tracking_no                    varchar(12) not null,
    shipping_date                  timestamp(0),
    data_kbn                       varchar(1) not null,
    process_flg                    numeric(1,0) not null,
    error_contents                 text,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_shipping_record_pk primary key (shipping_jisseki_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table work_shipping_record is '出荷実績ワーク';

comment on column work_shipping_record.shipping_jisseki_seq is '出荷実績連番';

comment on column work_shipping_record.order_no is '受注番号';

comment on column work_shipping_record.delivery_address_tel is '届け先＿電話番号';

comment on column work_shipping_record.delivery_address_name is '届け先＿名称';

comment on column work_shipping_record.tracking_no is '送り状番号';

comment on column work_shipping_record.shipping_date is '発送日';

comment on column work_shipping_record.data_kbn is 'データ区分';

comment on column work_shipping_record.process_flg is '処理フラグ';

comment on column work_shipping_record.error_contents is 'エラー内容';

comment on column work_shipping_record.orm_rowid is 'データ行ID';

comment on column work_shipping_record.created_user is '作成ユーザ';

comment on column work_shipping_record.created_datetime is '作成日時';

comment on column work_shipping_record.updated_user is '更新ユーザ';

comment on column work_shipping_record.updated_datetime is '更新日時';

