create table noshi
(
    shop_code                      varchar(16) not null,
    noshi_code                     varchar(16) not null,
    noshi_name                     varchar(40) not null,
    noshi_description              varchar(200),
    noshi_price                    numeric(8,0) not null,
    display_order                  numeric(8,0) not null,
    noshi_tax_type                 numeric(1,0) not null,
    display_flg                    numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint noshi_pk primary key (shop_code, noshi_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table noshi is '熨斗';

comment on column noshi.shop_code is 'ショップコード';

comment on column noshi.noshi_code is '熨斗コード';

comment on column noshi.noshi_name is '熨斗名称';

comment on column noshi.noshi_description is '熨斗説明';

comment on column noshi.noshi_price is '熨斗価格';

comment on column noshi.display_order is '表示順';

comment on column noshi.noshi_tax_type is '熨斗消費税区分';

comment on column noshi.display_flg is '表示フラグ';

comment on column noshi.orm_rowid is 'データ行ID';

comment on column noshi.created_user is '作成ユーザ';

comment on column noshi.created_datetime is '作成日時';

comment on column noshi.updated_user is '更新ユーザ';

comment on column noshi.updated_datetime is '更新日時';

create unique index noshi_ix0
on noshi
(orm_rowid)
tablespace ts_commodity_i01;

create unique index noshi_lpk
on noshi
(lower(shop_code), lower(noshi_code))
tablespace ts_commodity_i01;
