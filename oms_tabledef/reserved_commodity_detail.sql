create table reserved_commodity_detail
(
    shop_code                      varchar(16) not null,
    reflect_datetime_code          varchar(10) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16),
    unit_price                     numeric(8,0),
    discount_price                 numeric(8,0),
    reservation_price              numeric(8,0),
    jan_code                       varchar(16),
    standard_detail1_name          varchar(20),
    standard_detail2_name          varchar(20),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint res_commodity_detail_pk primary key (shop_code, reflect_datetime_code, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table reserved_commodity_detail is '商品情報変更予約商品詳細';

comment on column reserved_commodity_detail.shop_code is 'ショップコード';

comment on column reserved_commodity_detail.reflect_datetime_code is '反映日時コード';

comment on column reserved_commodity_detail.sku_code is 'SKUコード';

comment on column reserved_commodity_detail.commodity_code is '商品コード';

comment on column reserved_commodity_detail.unit_price is '商品単価';

comment on column reserved_commodity_detail.discount_price is '特別価格';

comment on column reserved_commodity_detail.reservation_price is '予約価格';

comment on column reserved_commodity_detail.jan_code is 'JANコード';

comment on column reserved_commodity_detail.standard_detail1_name is '規格詳細1名称';

comment on column reserved_commodity_detail.standard_detail2_name is '規格詳細2名称';

comment on column reserved_commodity_detail.orm_rowid is 'データ行ID';

comment on column reserved_commodity_detail.created_user is '作成ユーザ';

comment on column reserved_commodity_detail.created_datetime is '作成日時';

comment on column reserved_commodity_detail.updated_user is '更新ユーザ';

comment on column reserved_commodity_detail.updated_datetime is '更新日時';

create unique index res_commodity_detail_ix0
on reserved_commodity_detail
(orm_rowid)
tablespace ts_commodity_i01;
