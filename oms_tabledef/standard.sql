create table standard
(
    shop_code                      varchar(16) not null,
    standard_code                  varchar(16) not null,
    standard_name                  varchar(20) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint standard_pk primary key (shop_code, standard_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table standard is '規格';

comment on column standard.shop_code is 'ショップコード';

comment on column standard.standard_code is '規格コード';

comment on column standard.standard_name is '規格名称';

comment on column standard.orm_rowid is 'データ行ID';

comment on column standard.created_user is '作成ユーザ';

comment on column standard.created_datetime is '作成日時';

comment on column standard.updated_user is '更新ユーザ';

comment on column standard.updated_datetime is '更新日時';

create unique index standard_ix0
on standard
(orm_rowid)
tablespace ts_commodity_i01;

create unique index standard_lpk
on standard
(lower(shop_code), lower(standard_code))
tablespace ts_commodity_i01;
