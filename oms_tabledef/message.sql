create table message
(
    shop_code                      varchar(16) not null,
    message_code                   varchar(4) not null,
    delivery_note_message          varchar(1000),
    display_order                  numeric(8,0),
    message_use_method             varchar(2),
    marketing_channel              varchar(2),
    commodity_code                 varchar(16),
    category_code                  varchar(16),
    message_sales_method_type      varchar(2),
    shipping_method                varchar(2),
    ext_payment_method_type        varchar(2),
    message_enabled_flg            numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint message_pk primary key (shop_code, message_code) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table message is '納品書メッセージ';

comment on column message.shop_code is 'ショップコード';

comment on column message.message_code is 'メッセージコード';

comment on column message.delivery_note_message is '納品書メッセージ';

comment on column message.display_order is '表示順';

comment on column message.message_use_method is 'メッセージ使用方法';

comment on column message.marketing_channel is '販売経路';

comment on column message.commodity_code is '商品コード';

comment on column message.category_code is 'カテゴリコード';

comment on column message.message_sales_method_type is 'メッセージ適用販売方法区分';

comment on column message.shipping_method is '配送方法';

comment on column message.ext_payment_method_type is '支払方法区分（拡張）';

comment on column message.message_enabled_flg is 'メッセージ使用可能フラグ';

comment on column message.orm_rowid is 'データ行ID';

comment on column message.created_user is '作成ユーザ';

comment on column message.created_datetime is '作成日時';

comment on column message.updated_user is '更新ユーザ';

comment on column message.updated_datetime is '更新日時';

create unique index message_ix0
on message
(orm_rowid)
tablespace ts_shop_i01;
