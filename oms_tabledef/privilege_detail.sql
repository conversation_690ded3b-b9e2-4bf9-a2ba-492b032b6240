create table privilege_detail
(
    benefits_code                  varchar(10) not null,
    benefits_seq                   numeric(9,0) not null,
    benefits_kind                  varchar(1) not null,
    shop_code                      varchar(16),
    sku_code                       varchar(24),
    commodity_code                 varchar(16),
    present_qt                     numeric(3,0),
    discount_amount                numeric(8,0),
    discount_rate                  numeric(3,0),
    order_channel_kbn              varchar(1) not null,
    qt_from                        numeric(3,0),
    qt_to                          numeric(3,0),
    benefits_regular_cycle_joken_kbn varchar(2) not null,
    regular_kaiji                  numeric(5,0) not null,
    delivery_price                 numeric(10,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint privilege_detail_pk primary key (benefits_code, benefits_seq) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table privilege_detail is '特典詳細マスタ';

comment on column privilege_detail.benefits_code is '特典コード';

comment on column privilege_detail.benefits_seq is '特典連番';

comment on column privilege_detail.benefits_kind is '特典種別';

comment on column privilege_detail.shop_code is 'ショップコード';

comment on column privilege_detail.sku_code is 'SKUコード';

comment on column privilege_detail.commodity_code is '商品コード';

comment on column privilege_detail.present_qt is 'プレゼント数量';

comment on column privilege_detail.discount_amount is '値引額';

comment on column privilege_detail.discount_rate is '値引率';

comment on column privilege_detail.order_channel_kbn is '受注経路条件区分';

comment on column privilege_detail.qt_from is '数量（ＦＲＯＭ）';

comment on column privilege_detail.qt_to is '数量（ＴＯ）';

comment on column privilege_detail.benefits_regular_cycle_joken_kbn is '特典定期サイクル条件区分';

comment on column privilege_detail.regular_kaiji is '定期回次';

comment on column privilege_detail.delivery_price is '送料指定金額';

comment on column privilege_detail.orm_rowid is 'データ行ID';

comment on column privilege_detail.created_user is '作成ユーザ';

comment on column privilege_detail.created_datetime is '作成日時';

comment on column privilege_detail.updated_user is '更新ユーザ';

comment on column privilege_detail.updated_datetime is '更新日時';

create unique index privilege_detail_ix0
on privilege_detail
(orm_rowid)
tablespace ts_shop_i01;
