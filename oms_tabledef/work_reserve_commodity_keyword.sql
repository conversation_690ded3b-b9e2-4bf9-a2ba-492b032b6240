create table work_reserve_commodity_keyword
(
    shop_code                      varchar(16) not null,
    reflect_datetime_code          varchar(10) not null,
    commodity_code                 varchar(16) not null,
    search_keyword                 varchar(150) not null,
    constraint wk_reserve_commodity_keyword primary key (shop_code, reflect_datetime_code, commodity_code, search_keyword) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table work_reserve_commodity_keyword is '商品情報変更予約商品キーワード用ワークテーブル';

comment on column work_reserve_commodity_keyword.shop_code is 'ショップコード';

comment on column work_reserve_commodity_keyword.reflect_datetime_code is '反映日時コード';

comment on column work_reserve_commodity_keyword.commodity_code is '商品コード';

comment on column work_reserve_commodity_keyword.search_keyword is '検索キーワード';

