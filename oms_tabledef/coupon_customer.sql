create table coupon_customer
(
    coupon_management_code         varchar(16) not null,
    customer_code                  varchar(16) not null,
    neo_customer_no                varchar(12),
    coupon_issue_status            numeric(1,0) not null,
    coupon_used_count              numeric(8,0) not null,
    coupon_used_date               timestamp(0),
    baitai_code                    varchar(10),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint coupon_customer_pk primary key (coupon_management_code, customer_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table coupon_customer is 'クーポン顧客関連付け';

comment on column coupon_customer.coupon_management_code is 'クーポン管理コード';

comment on column coupon_customer.customer_code is '顧客コード';

comment on column coupon_customer.neo_customer_no is '顧客番号';

comment on column coupon_customer.coupon_issue_status is 'クーポン発行ステータス';

comment on column coupon_customer.coupon_used_count is 'クーポン累計利用回数';

comment on column coupon_customer.coupon_used_date is 'クーポン利用日';

comment on column coupon_customer.baitai_code is '媒体コード';

comment on column coupon_customer.orm_rowid is 'データ行ID';

comment on column coupon_customer.created_user is '作成ユーザ';

comment on column coupon_customer.created_datetime is '作成日時';

comment on column coupon_customer.updated_user is '更新ユーザ';

comment on column coupon_customer.updated_datetime is '更新日時';

create unique index coupon_customer_ix0
on coupon_customer
(orm_rowid)
tablespace ts_customer_i01;

create index coupon_customer_ix1
on coupon_customer
(customer_code)
tablespace ts_customer_i01;
