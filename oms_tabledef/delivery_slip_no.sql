create table delivery_slip_no
(
    shipping_no                    varchar(16) not null,
    delivery_no_seq                numeric(9,0) not null,
    order_no                       varchar(16) not null,
    tracking_no                    varchar(12) not null,
    delivery_address_tel           varchar(16) not null,
    shipping_date                  timestamp(0) not null,
    tracking_no_hand_flg           numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint delivery_slip_no_pk primary key (shipping_no, delivery_no_seq) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table delivery_slip_no is '配送伝票番号情報';

comment on column delivery_slip_no.shipping_no is '出荷番号';

comment on column delivery_slip_no.delivery_no_seq is '配送伝票番号連番';

comment on column delivery_slip_no.order_no is '受注番号';

comment on column delivery_slip_no.tracking_no is '送り状番号';

comment on column delivery_slip_no.delivery_address_tel is '届け先＿電話番号';

comment on column delivery_slip_no.shipping_date is '発送日';

comment on column delivery_slip_no.tracking_no_hand_flg is '送り状番号手動設定フラグ';

comment on column delivery_slip_no.orm_rowid is 'データ行ID';

comment on column delivery_slip_no.created_user is '作成ユーザ';

comment on column delivery_slip_no.created_datetime is '作成日時';

comment on column delivery_slip_no.updated_user is '更新ユーザ';

comment on column delivery_slip_no.updated_datetime is '更新日時';

create unique index delivery_slip_no_ix0
on delivery_slip_no
(orm_rowid)
tablespace ts_order_i01;

create index delivery_slip_no_ix1
on delivery_slip_no
(order_no)
tablespace ts_order_i01;

create index delivery_slip_no_ix2
on delivery_slip_no
(tracking_no)
tablespace ts_order_i01;
