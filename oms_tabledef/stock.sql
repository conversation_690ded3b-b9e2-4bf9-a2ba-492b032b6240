create table stock
(
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    allocated_warehouse_code       varchar(6) not null,
    commodity_code                 varchar(16) not null,
    wms_stock_quantity             numeric(8,0),
    stock_quantity                 numeric(8,0) not null,
    allocated_quantity             numeric(8,0) not null,
    reserved_quantity              numeric(8,0) not null,
    temporary_allocated_quantity   numeric(8,0) not null,
    arrival_reserved_quantity      numeric(8,0) not null,
    temporary_reserved_quantity    numeric(8,0) not null,
    reservation_limit              numeric(8,0),
    stock_threshold                numeric(8,0) not null,
    stock_arrival_date             timestamp(0),
    arrival_quantity               numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint stock_pk primary key (shop_code, sku_code, allocated_warehouse_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table stock is '在庫';

comment on column stock.shop_code is 'ショップコード';

comment on column stock.sku_code is 'SKUコード';

comment on column stock.allocated_warehouse_code is '引当倉庫コード';

comment on column stock.commodity_code is '商品コード';

comment on column stock.wms_stock_quantity is 'WMS在庫数量';

comment on column stock.stock_quantity is '在庫数量';

comment on column stock.allocated_quantity is '引当数量';

comment on column stock.reserved_quantity is '予約数量';

comment on column stock.temporary_allocated_quantity is '仮引当数量';

comment on column stock.arrival_reserved_quantity is '入荷予定予約数量';

comment on column stock.temporary_reserved_quantity is '仮予約数量';

comment on column stock.reservation_limit is '予約上限数';

comment on column stock.stock_threshold is '安全在庫';

comment on column stock.stock_arrival_date is '入荷予定日';

comment on column stock.arrival_quantity is '入荷予定数';

comment on column stock.orm_rowid is 'データ行ID';

comment on column stock.created_user is '作成ユーザ';

comment on column stock.created_datetime is '作成日時';

comment on column stock.updated_user is '更新ユーザ';

comment on column stock.updated_datetime is '更新日時';

create unique index stock_ix0
on stock
(orm_rowid)
tablespace ts_commodity_i01;

create unique index stock_ix1
on stock
(shop_code, sku_code, commodity_code)
tablespace ts_commodity_i01;

create index stock_ix2
on stock
(shop_code, commodity_code)
tablespace ts_commodity_i01;

create index stock_ix3
on stock
(shop_code, allocated_warehouse_code, stock_quantity)
tablespace ts_commodity_i01;

create index stock_ix4
on stock
(sku_code)
tablespace ts_commodity_i01;
