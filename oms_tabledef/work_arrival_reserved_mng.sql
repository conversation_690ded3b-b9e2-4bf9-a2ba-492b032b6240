create table work_arrival_reserved_mng
(
    shop_code                      varchar(16) not null,
    allocated_warehouse_code       varchar(6) not null,
    company_code                   varchar(4) not null,
    stock_arrival_date             timestamp(0) not null,
    center_code                    varchar(4) not null,
    agent_cd                       varchar(16),
    order_no                       varchar(12) not null,
    order_register_id              varchar(20),
    sku_code                       varchar(24) not null,
    order_quantity                 numeric(10,0),
    arrival_quantity               numeric(10,0),
    no_arrival_quantity            numeric(10,0),
    arrival_status                 varchar(1),
    comment_code                   varchar(50),
    reserves_flg1                  varchar(1),
    reserves_flg2                  varchar(1),
    reserves_flg3                  varchar(1),
    reserves_flg4                  varchar(6),
    reserves_flg5                  varchar(6),
    reserves_flg6                  varchar(6),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint work_arrival_reserved_mng_pk primary key (shop_code, allocated_warehouse_code, order_no, sku_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table work_arrival_reserved_mng is '入荷予実管理ワーク';

comment on column work_arrival_reserved_mng.shop_code is 'ショップコード';

comment on column work_arrival_reserved_mng.allocated_warehouse_code is '引当倉庫コード';

comment on column work_arrival_reserved_mng.company_code is '会社コード';

comment on column work_arrival_reserved_mng.stock_arrival_date is '入荷予定日';

comment on column work_arrival_reserved_mng.center_code is 'センターコード';

comment on column work_arrival_reserved_mng.agent_cd is '取扱店舗コード';

comment on column work_arrival_reserved_mng.order_no is '発注番号';

comment on column work_arrival_reserved_mng.order_register_id is '発注登録ID';

comment on column work_arrival_reserved_mng.sku_code is 'SKUコード';

comment on column work_arrival_reserved_mng.order_quantity is '発注数量';

comment on column work_arrival_reserved_mng.arrival_quantity is '入荷数量';

comment on column work_arrival_reserved_mng.no_arrival_quantity is '未入荷数量';

comment on column work_arrival_reserved_mng.arrival_status is '入荷完了フラグ';

comment on column work_arrival_reserved_mng.comment_code is 'コメントCODE';

comment on column work_arrival_reserved_mng.reserves_flg1 is '予備フラグ1';

comment on column work_arrival_reserved_mng.reserves_flg2 is '予備フラグ2';

comment on column work_arrival_reserved_mng.reserves_flg3 is '予備フラグ3';

comment on column work_arrival_reserved_mng.reserves_flg4 is '予備コード1';

comment on column work_arrival_reserved_mng.reserves_flg5 is '予備コード2';

comment on column work_arrival_reserved_mng.reserves_flg6 is '予備コード3';

comment on column work_arrival_reserved_mng.orm_rowid is 'データ行ID';

comment on column work_arrival_reserved_mng.created_user is '作成ユーザ';

comment on column work_arrival_reserved_mng.created_datetime is '作成日時';

comment on column work_arrival_reserved_mng.updated_user is '更新ユーザ';

comment on column work_arrival_reserved_mng.updated_datetime is '更新日時';

create unique index work_arrival_reserved_mng_ix0
on work_arrival_reserved_mng
(orm_rowid)
tablespace ts_commodity_i01;
