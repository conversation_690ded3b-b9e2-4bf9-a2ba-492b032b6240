create table mail_template_detail
(
    shop_code                      varchar(16) not null,
    mail_type                      varchar(2) not null,
    mail_template_no               numeric(8,0) not null,
    mail_template_branch_no        numeric(3,0) not null,
    parent_mail_template_branch_no numeric(3,0),
    mail_template_depth            numeric(1,0),
    mail_text                      text,
    mail_composition_name          varchar(20),
    substitution_tag               varchar(35),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint mail_template_detail_pk primary key (mail_template_branch_no, shop_code, mail_type, mail_template_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table mail_template_detail is 'メールテンプレート明細';

comment on column mail_template_detail.shop_code is 'ショップコード';

comment on column mail_template_detail.mail_type is 'メールタイプ';

comment on column mail_template_detail.mail_template_no is 'メールテンプレート番号';

comment on column mail_template_detail.mail_template_branch_no is 'メールテンプレート枝番';

comment on column mail_template_detail.parent_mail_template_branch_no is '親メールテンプレート枝番';

comment on column mail_template_detail.mail_template_depth is 'メールテンプレート階層';

comment on column mail_template_detail.mail_text is 'メール本文';

comment on column mail_template_detail.mail_composition_name is 'メール構成名称';

comment on column mail_template_detail.substitution_tag is '置換タグ';

comment on column mail_template_detail.orm_rowid is 'データ行ID';

comment on column mail_template_detail.created_user is '作成ユーザ';

comment on column mail_template_detail.created_datetime is '作成日時';

comment on column mail_template_detail.updated_user is '更新ユーザ';

comment on column mail_template_detail.updated_datetime is '更新日時';

create unique index mail_template_detail_ix0
on mail_template_detail
(orm_rowid)
tablespace ts_shop_i01;
