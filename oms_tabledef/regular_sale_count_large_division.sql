create table regular_sale_count_large_division
(
    customer_code                  varchar(16) not null,
    commodity_category_code        varchar(16) not null,
    buy_count                      numeric(5,0) not null,
    regular_kaiji                  numeric(5,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_count_large_division_pk primary key (customer_code, commodity_category_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table regular_sale_count_large_division is '定期回次大分類';

comment on column regular_sale_count_large_division.customer_code is '顧客コード';

comment on column regular_sale_count_large_division.commodity_category_code is '商品大分類';

comment on column regular_sale_count_large_division.buy_count is '購入回数';

comment on column regular_sale_count_large_division.regular_kaiji is '定期回次';

comment on column regular_sale_count_large_division.orm_rowid is 'データ行ID';

comment on column regular_sale_count_large_division.created_user is '作成ユーザ';

comment on column regular_sale_count_large_division.created_datetime is '作成日時';

comment on column regular_sale_count_large_division.updated_user is '更新ユーザ';

comment on column regular_sale_count_large_division.updated_datetime is '更新日時';

create unique index regular_sale_count_large_division_ix0
on regular_sale_count_large_division
(orm_rowid)
tablespace ts_customer_i01;
