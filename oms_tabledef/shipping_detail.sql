create table shipping_detail
(
    shipping_no                    varchar(16) not null,
    shipping_detail_no             numeric(16,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    unit_price                     numeric(8,0) not null,
    discount_price                 numeric(8,0),
    discount_amount                numeric(8,0),
    retail_price                   numeric(8,0) not null,
    retail_tax_group_code          varchar(8) not null,
    retail_tax_no                  numeric(3,0) not null,
    retail_tax_rate                numeric(3,0) not null,
    retail_tax                     numeric(10,2) not null,
    purchasing_amount              numeric(8,0) not null,
    gift_code                      varchar(16),
    gift_name                      varchar(40),
    gift_price                     numeric(8,0) not null,
    gift_tax_group_code            varchar(8) not null,
    gift_tax_no                    numeric(3,0) not null,
    gift_tax_rate                  numeric(3,0) not null,
    gift_tax                       numeric(10,2) not null,
    gift_tax_type                  numeric(1,0) not null,
    noshi_code                     varchar(16),
    noshi_name                     varchar(40),
    noshi_price                    numeric(8,0) not null,
    noshi_tax_group_code           varchar(8) not null,
    noshi_tax_no                   numeric(3,0) not null,
    noshi_tax_rate                 numeric(3,0) not null,
    noshi_tax                      numeric(10,2) not null,
    noshi_tax_type                 numeric(1,0) not null,
    noshi_nameplate                varchar(100),
    noshi_message                  varchar(200),
    air_transport_flg              numeric(1,0) not null,
    delivery_note_no_disp_flg      numeric(1,0) not null,
    hasso_souko_cd                 varchar(6),
    shipping_hold_kbn              varchar(1) not null,
    shipping_hold_date             timestamp(0),
    order_detail_no                numeric(16,0),
    tracking_out_flg               numeric(1,0) not null,
    souko_shiji                    varchar(40),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint shipping_detail_pk primary key (shipping_no, shipping_detail_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table shipping_detail is '出荷明細';

comment on column shipping_detail.shipping_no is '出荷番号';

comment on column shipping_detail.shipping_detail_no is '出荷明細番号';

comment on column shipping_detail.shop_code is 'ショップコード';

comment on column shipping_detail.sku_code is 'SKUコード';

comment on column shipping_detail.unit_price is '商品単価';

comment on column shipping_detail.discount_price is '特別価格';

comment on column shipping_detail.discount_amount is '値引額';

comment on column shipping_detail.retail_price is '販売価格';

comment on column shipping_detail.retail_tax_group_code is '販売時消費税グループコード';

comment on column shipping_detail.retail_tax_no is '販売時消費税番号';

comment on column shipping_detail.retail_tax_rate is '販売時消費税率';

comment on column shipping_detail.retail_tax is '販売時消費税額';

comment on column shipping_detail.purchasing_amount is '購入商品数';

comment on column shipping_detail.gift_code is 'ギフトコード';

comment on column shipping_detail.gift_name is 'ギフト名称';

comment on column shipping_detail.gift_price is 'ギフト価格';

comment on column shipping_detail.gift_tax_group_code is 'ギフト消費税グループコード';

comment on column shipping_detail.gift_tax_no is 'ギフト消費税番号';

comment on column shipping_detail.gift_tax_rate is 'ギフト消費税率';

comment on column shipping_detail.gift_tax is 'ギフト消費税額';

comment on column shipping_detail.gift_tax_type is 'ギフト消費税区分';

comment on column shipping_detail.noshi_code is '熨斗コード';

comment on column shipping_detail.noshi_name is '熨斗名称';

comment on column shipping_detail.noshi_price is '熨斗価格';

comment on column shipping_detail.noshi_tax_group_code is '熨斗消費税グループコード';

comment on column shipping_detail.noshi_tax_no is '熨斗消費税番号';

comment on column shipping_detail.noshi_tax_rate is '熨斗消費税率';

comment on column shipping_detail.noshi_tax is '熨斗消費税額';

comment on column shipping_detail.noshi_tax_type is '熨斗消費税区分';

comment on column shipping_detail.noshi_nameplate is '熨斗名入れ';

comment on column shipping_detail.noshi_message is '熨斗連絡事項';

comment on column shipping_detail.air_transport_flg is '空輸可否フラグ';

comment on column shipping_detail.delivery_note_no_disp_flg is '納品書非表示フラグ';

comment on column shipping_detail.hasso_souko_cd is '発送倉庫コード';

comment on column shipping_detail.shipping_hold_kbn is '発送保留区分';

comment on column shipping_detail.shipping_hold_date is '発送保留日';

comment on column shipping_detail.order_detail_no is '受注明細番号';

comment on column shipping_detail.tracking_out_flg is '送り状不要フラグ';

comment on column shipping_detail.souko_shiji is '倉庫指示';

comment on column shipping_detail.orm_rowid is 'データ行ID';

comment on column shipping_detail.created_user is '作成ユーザ';

comment on column shipping_detail.created_datetime is '作成日時';

comment on column shipping_detail.updated_user is '更新ユーザ';

comment on column shipping_detail.updated_datetime is '更新日時';

create unique index shipping_detail_ix0
on shipping_detail
(orm_rowid)
tablespace ts_order_i01;

create index shipping_detail_ix1
on shipping_detail
(shop_code)
tablespace ts_order_i01;

create index shipping_detail_ix2
on shipping_detail
(sku_code)
tablespace ts_order_i01;
