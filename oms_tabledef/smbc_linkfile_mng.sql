create table smbc_linkfile_mng
(
    smbc_send_mng_no               numeric(10,0) not null,
    smbc_function_kbn              varchar(1) not null,
    smbc_payment_method            varchar(2),
    smbc_mng_flg                   numeric(1,0) not null,
    get_target_ymd_from            varchar(8),
    get_target_hms_from            varchar(6),
    get_target_ymd_to              varchar(8),
    get_target_hms_to              varchar(6),
    smbc_send_file_name            varchar(256),
    smbc_send_datetime             timestamp(0),
    smbc_file_seq                  varchar(3),
    smbc_send_flg                  numeric(1,0) not null,
    smbc_recieve_flg               numeric(1,0) not null,
    smbc_proc_flg                  numeric(1,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint smbc_linkfile_mng_pk primary key (smbc_send_mng_no) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table smbc_linkfile_mng is 'SMBC連携ファイル管理';

comment on column smbc_linkfile_mng.smbc_send_mng_no is 'SMBC送信管理番号';

comment on column smbc_linkfile_mng.smbc_function_kbn is 'SMBC機能区分';

comment on column smbc_linkfile_mng.smbc_payment_method is '取得決済手段';

comment on column smbc_linkfile_mng.smbc_mng_flg is '管理対象フラグ';

comment on column smbc_linkfile_mng.get_target_ymd_from is '取得対象年月日ＦＲＯＭ';

comment on column smbc_linkfile_mng.get_target_hms_from is '取得対象時分秒ＦＲＯＭ';

comment on column smbc_linkfile_mng.get_target_ymd_to is '取得対象年月日ＴＯ';

comment on column smbc_linkfile_mng.get_target_hms_to is '取得対象時分秒ＴＯ';

comment on column smbc_linkfile_mng.smbc_send_file_name is 'SMBC送信ファイル名';

comment on column smbc_linkfile_mng.smbc_send_datetime is 'SMBC送信日時';

comment on column smbc_linkfile_mng.smbc_file_seq is 'SMBCファイル連番';

comment on column smbc_linkfile_mng.smbc_send_flg is 'SMBC送信フラグ';

comment on column smbc_linkfile_mng.smbc_recieve_flg is 'SMBC受信フラグ';

comment on column smbc_linkfile_mng.smbc_proc_flg is 'SMBC処理フラグ';

comment on column smbc_linkfile_mng.orm_rowid is 'データ行ID';

comment on column smbc_linkfile_mng.created_user is '作成ユーザ';

comment on column smbc_linkfile_mng.created_datetime is '作成日時';

comment on column smbc_linkfile_mng.updated_user is '更新ユーザ';

comment on column smbc_linkfile_mng.updated_datetime is '更新日時';

create unique index smbc_linkfile_mng_ix0
on smbc_linkfile_mng
(orm_rowid)
tablespace ts_shop_i01;
