create table reward_catalog_detail
(
    customer_code                  varchar(16) not null,
    order_no                       varchar(16) not null,
    order_detail_no                numeric(16,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    commodity_name                 varchar(100) not null,
    baitai_code                    varchar(10) not null,
    baitai_name                    varchar(50) not null,
    hinban_code                    varchar(24) not null,
    reduction_point                numeric(10,0) not null,
    purchasing_amount              numeric(8,0) not null,
    reduction_point_num            numeric(10,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint reward_catalog_detail_pk primary key (customer_code, order_no, order_detail_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table reward_catalog_detail is 'リワードカタログ特典交換明細';

comment on column reward_catalog_detail.customer_code is '顧客コード';

comment on column reward_catalog_detail.order_no is '受注番号';

comment on column reward_catalog_detail.order_detail_no is '受注明細番号';

comment on column reward_catalog_detail.shop_code is 'ショップコード';

comment on column reward_catalog_detail.sku_code is 'SKUコード';

comment on column reward_catalog_detail.commodity_code is '商品コード';

comment on column reward_catalog_detail.commodity_name is '商品名称';

comment on column reward_catalog_detail.baitai_code is '媒体コード';

comment on column reward_catalog_detail.baitai_name is '媒体名称';

comment on column reward_catalog_detail.hinban_code is '品番コード';

comment on column reward_catalog_detail.reduction_point is '利用ポイント数';

comment on column reward_catalog_detail.purchasing_amount is '購入商品数';

comment on column reward_catalog_detail.reduction_point_num is '利用ポイント数（小計）';

comment on column reward_catalog_detail.orm_rowid is 'データ行ID';

comment on column reward_catalog_detail.created_user is '作成ユーザ';

comment on column reward_catalog_detail.created_datetime is '作成日時';

comment on column reward_catalog_detail.updated_user is '更新ユーザ';

comment on column reward_catalog_detail.updated_datetime is '更新日時';

create unique index reward_catalog_detail_ix0
on reward_catalog_detail
(orm_rowid)
tablespace ts_customer_i01;

create index reward_catalog_detail_ix1
on reward_catalog_detail
(order_no, order_detail_no)
tablespace ts_customer_i01;
