create table enquete_choice
(
    enquete_code                   varchar(16) not null,
    enquete_question_no            numeric(8,0) not null,
    enquete_choices_no             numeric(8,0) not null,
    enquete_choices                varchar(200) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint enquete_choice_pk primary key (enquete_code, enquete_question_no, enquete_choices_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table enquete_choice is 'アンケート選択肢名';

comment on column enquete_choice.enquete_code is 'アンケートコード';

comment on column enquete_choice.enquete_question_no is 'アンケート設問番号';

comment on column enquete_choice.enquete_choices_no is 'アンケート選択肢番号';

comment on column enquete_choice.enquete_choices is 'アンケート選択肢名称';

comment on column enquete_choice.display_order is '表示順';

comment on column enquete_choice.orm_rowid is 'データ行ID';

comment on column enquete_choice.created_user is '作成ユーザ';

comment on column enquete_choice.created_datetime is '作成日時';

comment on column enquete_choice.updated_user is '更新ユーザ';

comment on column enquete_choice.updated_datetime is '更新日時';

create unique index enquete_choice_ix0
on enquete_choice
(orm_rowid)
tablespace ts_customer_i01;
