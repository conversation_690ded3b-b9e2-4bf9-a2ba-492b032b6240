create table inc_work_commodity
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    inc_work_code                  varchar(16) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint inc_work_commodity_pk primary key (shop_code, commodity_code, inc_work_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table inc_work_commodity is '付帯サービス対象商品';

comment on column inc_work_commodity.shop_code is 'ショップコード';

comment on column inc_work_commodity.commodity_code is '商品コード';

comment on column inc_work_commodity.inc_work_code is '付帯サービスコード';

comment on column inc_work_commodity.display_order is '表示順';

comment on column inc_work_commodity.orm_rowid is 'データ行ID';

comment on column inc_work_commodity.created_user is '作成ユーザ';

comment on column inc_work_commodity.created_datetime is '作成日時';

comment on column inc_work_commodity.updated_user is '更新ユーザ';

comment on column inc_work_commodity.updated_datetime is '更新日時';

create unique index inc_work_commodity_ix0
on inc_work_commodity
(orm_rowid)
tablespace ts_commodity_i01;
