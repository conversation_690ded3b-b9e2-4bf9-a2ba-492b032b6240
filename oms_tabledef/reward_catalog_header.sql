create table reward_catalog_header
(
    customer_code                  varchar(16) not null,
    order_no                       varchar(16) not null,
    neo_customer_no                varchar(12) not null,
    exchange_datetime              timestamp(0) not null,
    reduction_point_total          numeric(10,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint reward_catalog_header_pk primary key (customer_code, order_no) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table reward_catalog_header is 'リワードカタログ特典交換ヘッダ';

comment on column reward_catalog_header.customer_code is '顧客コード';

comment on column reward_catalog_header.order_no is '受注番号';

comment on column reward_catalog_header.neo_customer_no is '顧客番号';

comment on column reward_catalog_header.exchange_datetime is '交換申込日時';

comment on column reward_catalog_header.reduction_point_total is '利用ポイント数（合計）';

comment on column reward_catalog_header.orm_rowid is 'データ行ID';

comment on column reward_catalog_header.created_user is '作成ユーザ';

comment on column reward_catalog_header.created_datetime is '作成日時';

comment on column reward_catalog_header.updated_user is '更新ユーザ';

comment on column reward_catalog_header.updated_datetime is '更新日時';

create unique index reward_catalog_header_ix0
on reward_catalog_header
(orm_rowid)
tablespace ts_customer_i01;

create index reward_catalog_header_ix1
on reward_catalog_header
(order_no)
tablespace ts_customer_i01;

create index reward_catalog_header_ix2
on reward_catalog_header
(neo_customer_no)
tablespace ts_customer_i01;
