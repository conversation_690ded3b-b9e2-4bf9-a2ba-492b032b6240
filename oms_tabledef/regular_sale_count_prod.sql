create table regular_sale_count_prod
(
    customer_code                  varchar(16) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    buy_count                      numeric(5,0) not null,
    regular_kaiji                  numeric(5,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_count_prod_pk primary key (customer_code, shop_code, sku_code) using index
        tablespace ts_customer_i01
)
tablespace ts_customer_t01;

comment on table regular_sale_count_prod is '定期回次商品';

comment on column regular_sale_count_prod.customer_code is '顧客コード';

comment on column regular_sale_count_prod.shop_code is 'ショップコード';

comment on column regular_sale_count_prod.sku_code is 'SKUコード';

comment on column regular_sale_count_prod.buy_count is '購入回数';

comment on column regular_sale_count_prod.regular_kaiji is '定期回次';

comment on column regular_sale_count_prod.orm_rowid is 'データ行ID';

comment on column regular_sale_count_prod.created_user is '作成ユーザ';

comment on column regular_sale_count_prod.created_datetime is '作成日時';

comment on column regular_sale_count_prod.updated_user is '更新ユーザ';

comment on column regular_sale_count_prod.updated_datetime is '更新日時';

create unique index regular_sale_count_prod_ix0
on regular_sale_count_prod
(orm_rowid)
tablespace ts_customer_i01;
