create table review_post
(
    review_id                      numeric(38,0) not null,
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    customer_code                  varchar(16) not null,
    review_title                   varchar(50) not null,
    nickname                       varchar(15) not null,
    review_contributed_datetime    timestamp(0) not null,
    review_description             text not null,
    review_score                   numeric(3,0) not null,
    review_display_type            numeric(1,0) not null,
    review_point_allocated_status  numeric(1,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint review_post_pk primary key (review_id) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table review_post is 'レビュー投稿';

comment on column review_post.review_id is 'レビューID';

comment on column review_post.shop_code is 'ショップコード';

comment on column review_post.commodity_code is '商品コード';

comment on column review_post.customer_code is '顧客コード';

comment on column review_post.review_title is 'レビュータイトル';

comment on column review_post.nickname is 'ニックネーム';

comment on column review_post.review_contributed_datetime is 'レビュー投稿日時';

comment on column review_post.review_description is 'レビュー内容';

comment on column review_post.review_score is 'レビュー点数';

comment on column review_post.review_display_type is '商品レビュー表示区分';

comment on column review_post.review_point_allocated_status is 'レビューポイント割当ステータス';

comment on column review_post.orm_rowid is 'データ行ID';

comment on column review_post.created_user is '作成ユーザ';

comment on column review_post.created_datetime is '作成日時';

comment on column review_post.updated_user is '更新ユーザ';

comment on column review_post.updated_datetime is '更新日時';

create unique index review_post_ix0
on review_post
(orm_rowid)
tablespace ts_commodity_i01;

create index review_post_ix1
on review_post
(shop_code, commodity_code, customer_code)
tablespace ts_commodity_i01;
