create table recommended_commodity
(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    customer_code                  varchar(16) not null,
    display_order                  numeric(8,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint recommended_commodity_pk primary key (shop_code, commodity_code, customer_code) using index
        tablespace ts_commodity_i01
)
tablespace ts_commodity_t01;

comment on table recommended_commodity is '個別リコメンド';

comment on column recommended_commodity.shop_code is 'ショップコード';

comment on column recommended_commodity.commodity_code is '商品コード';

comment on column recommended_commodity.customer_code is '顧客コード';

comment on column recommended_commodity.display_order is '表示順';

comment on column recommended_commodity.orm_rowid is 'データ行ID';

comment on column recommended_commodity.created_user is '作成ユーザ';

comment on column recommended_commodity.created_datetime is '作成日時';

comment on column recommended_commodity.updated_user is '更新ユーザ';

comment on column recommended_commodity.updated_datetime is '更新日時';

create unique index recommended_commodity_ix0
on recommended_commodity
(orm_rowid)
tablespace ts_commodity_i01;
