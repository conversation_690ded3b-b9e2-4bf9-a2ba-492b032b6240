create table head_office_payment_detail_data
(
    id                             numeric(28,0) not null,
    report_id                      varchar(28) not null,
    printed_flag                   varchar(5),
    printed_date                   varchar(30),
    col01                          varchar(4000),
    col02                          varchar(4000),
    col03                          varchar(4000),
    col04                          varchar(4000),
    col05                          varchar(4000),
    col06                          varchar(4000),
    col07                          varchar(4000),
    col08                          varchar(4000),
    col09                          varchar(4000),
    col10                          varchar(4000),
    col11                          varchar(4000),
    col12                          varchar(4000),
    col13                          varchar(4000),
    col14                          varchar(4000),
    col15                          varchar(4000),
    col16                          varchar(4000),
    col17                          varchar(4000),
    col18                          varchar(4000),
    col19                          varchar(4000),
    col20                          varchar(4000),
    col21                          varchar(4000),
    col22                          varchar(4000),
    col23                          varchar(4000),
    col24                          varchar(4000),
    col25                          varchar(4000),
    col26                          varchar(4000),
    col27                          varchar(4000),
    col28                          varchar(4000),
    col29                          varchar(4000),
    col30                          varchar(4000),
    col31                          varchar(4000),
    col32                          varchar(4000),
    col33                          varchar(4000),
    col34                          varchar(4000),
    col35                          varchar(4000),
    constraint head_office_payment_detail_data_pk primary key (id, report_id) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table head_office_payment_detail_data is '本社出し請求明細データ';

comment on column head_office_payment_detail_data.id is 'ID';

comment on column head_office_payment_detail_data.report_id is 'REPORT_ID';

comment on column head_office_payment_detail_data.printed_flag is 'PRINTED_FLAG';

comment on column head_office_payment_detail_data.printed_date is 'PRINTED_DATE';

comment on column head_office_payment_detail_data.col01 is '発行年月日';

comment on column head_office_payment_detail_data.col02 is '受付日';

comment on column head_office_payment_detail_data.col03 is 'お客様コード';

comment on column head_office_payment_detail_data.col04 is 'お客様コード枝番';

comment on column head_office_payment_detail_data.col05 is 'お客様郵便番号';

comment on column head_office_payment_detail_data.col06 is 'お客様住所１';

comment on column head_office_payment_detail_data.col07 is 'お客様住所２';

comment on column head_office_payment_detail_data.col08 is 'カスタマーバーコード';

comment on column head_office_payment_detail_data.col09 is 'お客様名';

comment on column head_office_payment_detail_data.col10 is 'ご注文No';

comment on column head_office_payment_detail_data.col11 is '受付担当者';

comment on column head_office_payment_detail_data.col12 is '出荷日';

comment on column head_office_payment_detail_data.col13 is '送り先';

comment on column head_office_payment_detail_data.col14 is '商品コード';

comment on column head_office_payment_detail_data.col15 is '商品名';

comment on column head_office_payment_detail_data.col16 is '数量';

comment on column head_office_payment_detail_data.col17 is '単価';

comment on column head_office_payment_detail_data.col18 is '金額';

comment on column head_office_payment_detail_data.col19 is '送料';

comment on column head_office_payment_detail_data.col20 is '当社使用欄';

comment on column head_office_payment_detail_data.col21 is 'お買い上げ合計';

comment on column head_office_payment_detail_data.col22 is '送料合計';

comment on column head_office_payment_detail_data.col23 is '消費税';

comment on column head_office_payment_detail_data.col24 is 'ご請求金額';

comment on column head_office_payment_detail_data.col25 is 'お支払い期限';

comment on column head_office_payment_detail_data.col26 is '1行目OCR印字部';

comment on column head_office_payment_detail_data.col27 is '2行目OCR印字部';

comment on column head_office_payment_detail_data.col28 is 'バーコード';

comment on column head_office_payment_detail_data.col29 is '振替予定日';

comment on column head_office_payment_detail_data.col30 is '役職';

comment on column head_office_payment_detail_data.col31 is 'コメント';

comment on column head_office_payment_detail_data.col32 is '発行済み区分';

comment on column head_office_payment_detail_data.col33 is '送料消費税';

comment on column head_office_payment_detail_data.col34 is '顧客分類';

comment on column head_office_payment_detail_data.col35 is '支払区分';

