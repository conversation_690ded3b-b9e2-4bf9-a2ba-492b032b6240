create table role_header
(
    shop_code                      varchar(16) not null,
    role_id                        varchar(8) not null,
    role_name                      varchar(50) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint role_header_pk primary key (shop_code, role_id) using index
        tablespace ts_shop_i01
)
tablespace ts_shop_t01;

comment on table role_header is 'ロールヘッダ';

comment on column role_header.shop_code is 'ショップコード';

comment on column role_header.role_id is 'ロールID';

comment on column role_header.role_name is 'ロール名';

comment on column role_header.orm_rowid is 'データ行ID';

comment on column role_header.created_user is '作成ユーザ';

comment on column role_header.created_datetime is '作成日時';

comment on column role_header.updated_user is '更新ユーザ';

comment on column role_header.updated_datetime is '更新日時';

create unique index role_header_ix0
on role_header
(orm_rowid)
tablespace ts_shop_i01;
