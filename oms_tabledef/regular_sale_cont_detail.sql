create table regular_sale_cont_detail
(
    regular_contract_no            varchar(14) not null,
    regular_contract_detail_no     numeric(3,0) not null,
    shop_code                      varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    contract_amount                numeric(8,0) not null,
    commodity_name                 varchar(100) not null,
    commodity_subcategory_code     varchar(16),
    commodity_subcategory_code_name varchar(50),
    baitai_code                    varchar(10) not null,
    regular_cycle_delivery_kbn     varchar(1) not null,
    regular_cycle_kijun_date       timestamp(0),
    regular_kind                   varchar(1) not null,
    regular_cycle_day_int          varchar(2),
    regular_cycle_day              numeric(2,0),
    regular_cycle_mon_interval     numeric(2,0),
    regular_cycle_mon_interval_day numeric(2,0),
    regular_cycle_week_num         numeric(2,0),
    regular_cycle_week_kbn         varchar(1),
    regular_cycle_week_mon         varchar(1),
    regular_cycle_week_tue         varchar(1),
    regular_cycle_week_wed         varchar(1),
    regular_cycle_week_thu         varchar(1),
    regular_cycle_week_fri         varchar(1),
    regular_cycle_week_sat         varchar(1),
    regular_cycle_week_sun         varchar(1),
    regular_cycle_week_hol         varchar(1),
    cycle_disp_name                varchar(1000),
    next_shipping_plan_date        timestamp(0),
    next_shipping_date             timestamp(0) not null,
    next_delivery_plan_date        timestamp(0),
    next_delivery_date             timestamp(0),
    lastest_delivery_date          timestamp(0),
    regular_kaiji                  numeric(5,0) not null,
    shipped_regular_count          numeric(3,0) not null,
    regular_sale_stop_from         numeric(5,0),
    regular_sale_stop_to           numeric(5,0),
    hasso_souko_cd                 varchar(6),
    shipping_area                  varchar(2),
    regular_check_memo             varchar(1000),
    regular_memo_hold_flg          numeric(1,0) not null,
    souko_shiji                    varchar(40),
    next_regular_sale_stop_status  varchar(1) DEFAULT '0' not null,
    regular_stop_reason_kbn        varchar(2),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null,
    constraint regular_sale_cont_detail_pk primary key (regular_contract_no, regular_contract_detail_no) using index
        tablespace ts_order_i01
)
tablespace ts_order_t01;

comment on table regular_sale_cont_detail is '定期契約明細';

comment on column regular_sale_cont_detail.regular_contract_no is '定期契約番号';

comment on column regular_sale_cont_detail.regular_contract_detail_no is '定期契約明細番号';

comment on column regular_sale_cont_detail.shop_code is 'ショップコード';

comment on column regular_sale_cont_detail.sku_code is 'SKUコード';

comment on column regular_sale_cont_detail.commodity_code is '商品コード';

comment on column regular_sale_cont_detail.contract_amount is '契約商品数';

comment on column regular_sale_cont_detail.commodity_name is '商品名称';

comment on column regular_sale_cont_detail.commodity_subcategory_code is '商品中分類';

comment on column regular_sale_cont_detail.commodity_subcategory_code_name is '商品中分類名称';

comment on column regular_sale_cont_detail.baitai_code is '媒体コード';

comment on column regular_sale_cont_detail.regular_cycle_delivery_kbn is '定期サイクル発送日指定区分';

comment on column regular_sale_cont_detail.regular_cycle_kijun_date is '定期サイクル基準日';

comment on column regular_sale_cont_detail.regular_kind is '定期サイクル種別';

comment on column regular_sale_cont_detail.regular_cycle_day_int is '定期サイクル日付間隔';

comment on column regular_sale_cont_detail.regular_cycle_day is '定期サイクル日付';

comment on column regular_sale_cont_detail.regular_cycle_mon_interval is '定期サイクル月間隔';

comment on column regular_sale_cont_detail.regular_cycle_mon_interval_day is '定期サイクル月間隔日付';

comment on column regular_sale_cont_detail.regular_cycle_week_num is '定期サイクル第○週';

comment on column regular_sale_cont_detail.regular_cycle_week_kbn is '定期サイクル○曜日';

comment on column regular_sale_cont_detail.regular_cycle_week_mon is '定期サイクル届指定月';

comment on column regular_sale_cont_detail.regular_cycle_week_tue is '定期サイクル届指定火';

comment on column regular_sale_cont_detail.regular_cycle_week_wed is '定期サイクル届指定水';

comment on column regular_sale_cont_detail.regular_cycle_week_thu is '定期サイクル届指定木';

comment on column regular_sale_cont_detail.regular_cycle_week_fri is '定期サイクル届指定金';

comment on column regular_sale_cont_detail.regular_cycle_week_sat is '定期サイクル届指定土';

comment on column regular_sale_cont_detail.regular_cycle_week_sun is '定期サイクル届指定日';

comment on column regular_sale_cont_detail.regular_cycle_week_hol is '定期サイクル届指定祝日';

comment on column regular_sale_cont_detail.cycle_disp_name is 'サイクル表示名';

comment on column regular_sale_cont_detail.next_shipping_plan_date is '次回発送予定日';

comment on column regular_sale_cont_detail.next_shipping_date is '次回発送日';

comment on column regular_sale_cont_detail.next_delivery_plan_date is '次回お届け予定日';

comment on column regular_sale_cont_detail.next_delivery_date is '次回お届け日';

comment on column regular_sale_cont_detail.lastest_delivery_date is '最新お届け日';

comment on column regular_sale_cont_detail.regular_kaiji is '定期回次';

comment on column regular_sale_cont_detail.shipped_regular_count is '発送済定期回数';

comment on column regular_sale_cont_detail.regular_sale_stop_from is '定期休止回次FROM';

comment on column regular_sale_cont_detail.regular_sale_stop_to is '定期休止回次TO';

comment on column regular_sale_cont_detail.hasso_souko_cd is '発送倉庫コード';

comment on column regular_sale_cont_detail.shipping_area is '発送場所';

comment on column regular_sale_cont_detail.regular_check_memo is '定期チェックメモ';

comment on column regular_sale_cont_detail.regular_memo_hold_flg is '定期メモ保留フラグ';

comment on column regular_sale_cont_detail.souko_shiji is '倉庫指示';

comment on column regular_sale_cont_detail.next_regular_sale_stop_status is '次回定期休止ステータス';

comment on column regular_sale_cont_detail.regular_stop_reason_kbn is '定期休止理由区分';

comment on column regular_sale_cont_detail.orm_rowid is 'データ行ID';

comment on column regular_sale_cont_detail.created_user is '作成ユーザ';

comment on column regular_sale_cont_detail.created_datetime is '作成日時';

comment on column regular_sale_cont_detail.updated_user is '更新ユーザ';

comment on column regular_sale_cont_detail.updated_datetime is '更新日時';

create unique index regular_sale_cont_detail_ix0
on regular_sale_cont_detail
(orm_rowid)
tablespace ts_order_i01;

create index regular_sale_cont_detail_ix1
on regular_sale_cont_detail
(shop_code)
tablespace ts_order_i01;

create index regular_sale_cont_detail_ix2
on regular_sale_cont_detail
(sku_code)
tablespace ts_order_i01;

create index regular_sale_cont_detail_ix3
on regular_sale_cont_detail
(commodity_code)
tablespace ts_order_i01;

create index regular_sale_cont_detail_ix4
on regular_sale_cont_detail
(baitai_code)
tablespace ts_order_i01;
