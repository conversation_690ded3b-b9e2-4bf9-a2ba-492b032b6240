# OMSテーブル構造比較レポート

## 概要

OMSシステムのDDL（oms_tabledef配下）と開発環境のDBスキーマ（oms_readreplica）のテーブル構造比較を実施しました。

**実行日時**: 2025-07-01  
**比較対象**: IF-DW番号指定の10テーブル  
**接続先**: localhost:15432 (dlpf-dev) / oms_readreplicaスキーマ  

## 比較結果サマリー

| IF番号 | テーブル名 | 説明 | 結果 |
|--------|------------|------|------|
| IF-DW102-DF01 | category | カテゴリ | ✅ 完全一致 |
| IF-DW103-DF01 | category_commodity | カテゴリ陳列商品 | ✅ 完全一致 |
| IF-DW104-DF01 | card_info | カード情報 | ✅ 完全一致 |
| IF-DW106-DF01 | campaign_customer | キャンペーン設定顧客 | ✅ 完全一致 |
| IF-DW108-DF01 | coupon_customer | クーポン顧客関連付け | ✅ 完全一致 |
| IF-DW116-DF01 | shipping_detail | 出荷明細 | ✅ 完全一致 |
| IF-DW117-DF01 | shipping_detail_composition | 出荷明細構成品 | ✅ 完全一致 |
| IF-DW118-DF01 | order_campaign_use_amount | 受注キャンペーンクーポン利用額 | ✅ 完全一致 |
| IF-DW119-DF01 | order_campaign | 受注キャンペーン適用 | ✅ 完全一致 |
| IF-DW126-DF01 | stock | 在庫 | ✅ 完全一致 |

## 総合結果

- **総合結果**: ✅ 全テーブル一致
- **比較対象テーブル数**: 10
- **一致テーブル数**: 10  
- **差分テーブル数**: 0

## 詳細比較結果

### IF-DW102-DF01: category (カテゴリ)
- **DB側列数**: 16
- **DDL側列数**: 16
- **結果**: ✅ 完全一致

### IF-DW103-DF01: category_commodity (カテゴリ陳列商品)
- **DB側列数**: 16
- **DDL側列数**: 16
- **結果**: ✅ 完全一致

### IF-DW104-DF01: card_info (カード情報)
- **DB側列数**: 23
- **DDL側列数**: 23
- **結果**: ✅ 完全一致

### IF-DW106-DF01: campaign_customer (キャンペーン設定顧客)
- **DB側列数**: 8
- **DDL側列数**: 8
- **結果**: ✅ 完全一致

### IF-DW108-DF01: coupon_customer (クーポン顧客関連付け)
- **DB側列数**: 12
- **DDL側列数**: 12
- **結果**: ✅ 完全一致

### IF-DW116-DF01: shipping_detail (出荷明細)
- **DB側列数**: 44
- **DDL側列数**: 44
- **結果**: ✅ 完全一致

### IF-DW117-DF01: shipping_detail_composition (出荷明細構成品)
- **DB側列数**: 31
- **DDL側列数**: 31
- **結果**: ✅ 完全一致

### IF-DW118-DF01: order_campaign_use_amount (受注キャンペーンクーポン利用額)
- **DB側列数**: 12
- **DDL側列数**: 12
- **結果**: ✅ 完全一致

### IF-DW119-DF01: order_campaign (受注キャンペーン適用)
- **DB側列数**: 10
- **DDL側列数**: 10
- **結果**: ✅ 完全一致

### IF-DW126-DF01: stock (在庫)
- **DB側列数**: 20
- **DDL側列数**: 20
- **結果**: ✅ 完全一致

## 結論

指定された10テーブル全てにおいて、OMSシステムのDDLファイル（oms_tabledef配下）と開発環境のDBスキーマ（oms_readreplica）のテーブル構造が完全に一致していることを確認しました。

- 列数、列名、データ型、NULL制約が全て一致
- 差分は検出されませんでした
- DDLファイルと実際のDB構造に乖離はありません

## 使用ツール

- **比較スクリプト**: `compare_oms_tables.py`
- **DB接続**: psycopg2
- **比較対象**: information_schema.columns vs DDLファイル解析
- **検証項目**: 列名、データ型、NULL制約、列数

## 備考

- 開発環境DB接続: localhost:15432 (ポートフォワード)
- 認証情報: webshop/password
- スキーマ: oms_readreplica
- DDLファイル場所: oms_tabledef/*.sql
