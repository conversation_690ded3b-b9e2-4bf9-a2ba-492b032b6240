=== OMSテーブル構造比較レポート ===

## IF-DW102-DF01: category (カテゴリ)
------------------------------------------------------------
DB側列数: 16
DDL側列数: 16
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| category_code | varchar(16) | × |
| category_name_pc | varchar(20) | × |
| category_name_sp | varchar(10) | × |
| parent_category_code | varchar(16) | × |
| path | varchar(256) | × |
| depth | numeric(2,0) | × |
| display_order | numeric(8,0) | × |
| commodity_count | numeric(12,0) | ○ |
| last_related_count_datetime | timestamp(0) | ○ |
| public_commodity_count | numeric(12,0) | ○ |
| last_public_count_datetime | timestamp(0) | ○ |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW103-DF01: category_commodity (カテゴリ陳列商品)
------------------------------------------------------------
DB側列数: 16
DDL側列数: 16
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| shop_code | varchar(16) | × |
| category_code | varchar(16) | × |
| commodity_code | varchar(16) | × |
| category_search_path | varchar(256) | × |
| search_category_code0 | varchar(16) | × |
| search_category_code1 | varchar(16) | × |
| search_category_code2 | varchar(16) | × |
| search_category_code3 | varchar(16) | × |
| search_category_code4 | varchar(16) | × |
| search_category_code5 | varchar(16) | × |
| search_category_code6 | varchar(16) | × |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW104-DF01: card_info (カード情報)
------------------------------------------------------------
DB側列数: 23
DDL側列数: 23
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| customer_code | varchar(16) | × |
| credit_card_kanri_no | varchar(12) | × |
| credit_card_kanri_detail_no | varchar(4) | × |
| credit_card_no | varchar(50) | × |
| card_expire_year | varchar(4) | ○ |
| card_expire_month | varchar(2) | ○ |
| card_holder | varchar(150) | ○ |
| card_brand | varchar(2) | × |
| default_use_flag | numeric(1,0) | × |
| change_datetime | timestamp(0) | × |
| change_channel_kbn | varchar(2) | × |
| card_keep_ng_flg | numeric(1,0) | × |
| change_reason_kbn | varchar(2) | × |
| change_reason | varchar(200) | × |
| change_user_code | numeric(38,0) | ○ |
| dhc_card_flg | numeric(1,0) | × |
| crm_card_id | varchar(20) | ○ |
| crm_card_updated_datetime | varchar(24) | ○ |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW106-DF01: campaign_customer (キャンペーン設定顧客)
------------------------------------------------------------
DB側列数: 8
DDL側列数: 8
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| campaign_instructions_code | varchar(16) | × |
| customer_code | varchar(16) | × |
| joken_type | varchar(1) | × |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW108-DF01: coupon_customer (クーポン顧客関連付け)
------------------------------------------------------------
DB側列数: 12
DDL側列数: 12
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| coupon_management_code | varchar(16) | × |
| customer_code | varchar(16) | × |
| neo_customer_no | varchar(12) | ○ |
| coupon_issue_status | numeric(1,0) | × |
| coupon_used_count | numeric(8,0) | × |
| coupon_used_date | timestamp(0) | ○ |
| baitai_code | varchar(10) | ○ |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW116-DF01: shipping_detail (出荷明細)
------------------------------------------------------------
DB側列数: 44
DDL側列数: 44
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| shipping_no | varchar(16) | × |
| shipping_detail_no | numeric(16,0) | × |
| shop_code | varchar(16) | × |
| sku_code | varchar(24) | × |
| unit_price | numeric(8,0) | × |
| discount_price | numeric(8,0) | ○ |
| discount_amount | numeric(8,0) | ○ |
| retail_price | numeric(8,0) | × |
| retail_tax_group_code | varchar(8) | × |
| retail_tax_no | numeric(3,0) | × |
| retail_tax_rate | numeric(3,0) | × |
| retail_tax | numeric(10,2) | × |
| purchasing_amount | numeric(8,0) | × |
| gift_code | varchar(16) | ○ |
| gift_name | varchar(40) | ○ |
| gift_price | numeric(8,0) | × |
| gift_tax_group_code | varchar(8) | × |
| gift_tax_no | numeric(3,0) | × |
| gift_tax_rate | numeric(3,0) | × |
| gift_tax | numeric(10,2) | × |
| gift_tax_type | numeric(1,0) | × |
| noshi_code | varchar(16) | ○ |
| noshi_name | varchar(40) | ○ |
| noshi_price | numeric(8,0) | × |
| noshi_tax_group_code | varchar(8) | × |
| noshi_tax_no | numeric(3,0) | × |
| noshi_tax_rate | numeric(3,0) | × |
| noshi_tax | numeric(10,2) | × |
| noshi_tax_type | numeric(1,0) | × |
| noshi_nameplate | varchar(100) | ○ |
| noshi_message | varchar(200) | ○ |
| air_transport_flg | numeric(1,0) | × |
| delivery_note_no_disp_flg | numeric(1,0) | × |
| hasso_souko_cd | varchar(6) | ○ |
| shipping_hold_kbn | varchar(1) | × |
| shipping_hold_date | timestamp(0) | ○ |
| order_detail_no | numeric(16,0) | ○ |
| tracking_out_flg | numeric(1,0) | × |
| souko_shiji | varchar(40) | ○ |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW117-DF01: shipping_detail_composition (出荷明細構成品)
------------------------------------------------------------
DB側列数: 31
DDL側列数: 31
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| shipping_no | varchar(16) | × |
| shipping_detail_no | numeric(16,0) | × |
| composition_no | numeric(2,0) | × |
| shop_code | varchar(16) | × |
| parent_commodity_code | varchar(16) | × |
| parent_sku_code | varchar(24) | × |
| child_commodity_code | varchar(16) | × |
| child_sku_code | varchar(24) | × |
| commodity_name | varchar(100) | × |
| standard_detail1_name | varchar(20) | ○ |
| standard_detail2_name | varchar(20) | ○ |
| unit_price | numeric(8,0) | × |
| discount_amount | numeric(8,0) | ○ |
| retail_price | numeric(8,0) | × |
| retail_tax | numeric(10,2) | × |
| commodity_tax_group_code | varchar(8) | × |
| commodity_tax_no | numeric(3,0) | × |
| commodity_tax_rate | numeric(3,0) | × |
| commodity_tax | numeric(10,2) | × |
| commodity_tax_type | numeric(1,0) | × |
| composition_quantity | numeric(2,0) | × |
| stock_management_type | numeric(1,0) | × |
| stock_allocated_kbn | varchar(2) | × |
| allocated_warehouse_code | varchar(6) | ○ |
| allocated_quantity | numeric(8,0) | × |
| arrival_reserved_quantity | numeric(8,0) | × |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW118-DF01: order_campaign_use_amount (受注キャンペーンクーポン利用額)
------------------------------------------------------------
DB側列数: 12
DDL側列数: 12
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| order_no | varchar(16) | × |
| tax_group_code | varchar(8) | × |
| tax_no | numeric(3,0) | × |
| use_code_type | varchar(1) | × |
| use_code | varchar(16) | × |
| use_amount | numeric(8,0) | × |
| tax_rate | numeric(3,0) | × |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW119-DF01: order_campaign (受注キャンペーン適用)
------------------------------------------------------------
DB側列数: 10
DDL側列数: 10
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| order_no | varchar(16) | × |
| campaign_instructions_code | varchar(16) | × |
| campaign_instructions_name | varchar(50) | × |
| campaign_description | varchar(100) | ○ |
| campaign_end_date | timestamp(0) | × |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

## IF-DW126-DF01: stock (在庫)
------------------------------------------------------------
DB側列数: 20
DDL側列数: 20
✅ **完全一致**

### 列詳細情報:
| 列名 | データ型 | NULL許可 |
|------|----------|----------|
| shop_code | varchar(16) | × |
| sku_code | varchar(24) | × |
| allocated_warehouse_code | varchar(6) | × |
| commodity_code | varchar(16) | × |
| wms_stock_quantity | numeric(8,0) | ○ |
| stock_quantity | numeric(8,0) | × |
| allocated_quantity | numeric(8,0) | × |
| reserved_quantity | numeric(8,0) | × |
| temporary_allocated_quantity | numeric(8,0) | × |
| arrival_reserved_quantity | numeric(8,0) | × |
| temporary_reserved_quantity | numeric(8,0) | × |
| reservation_limit | numeric(8,0) | ○ |
| stock_threshold | numeric(8,0) | × |
| stock_arrival_date | timestamp(0) | ○ |
| arrival_quantity | numeric(8,0) | ○ |
| orm_rowid | numeric(38,0) | × |
| created_user | varchar(100) | × |
| created_datetime | timestamp(3) | × |
| updated_user | varchar(100) | × |
| updated_datetime | timestamp(3) | × |

============================================================
=== 比較結果サマリー ===
============================================================
✅ IF-DW102-DF01: category - 完全一致
✅ IF-DW103-DF01: category_commodity - 完全一致
✅ IF-DW104-DF01: card_info - 完全一致
✅ IF-DW106-DF01: campaign_customer - 完全一致
✅ IF-DW108-DF01: coupon_customer - 完全一致
✅ IF-DW116-DF01: shipping_detail - 完全一致
✅ IF-DW117-DF01: shipping_detail_composition - 完全一致
✅ IF-DW118-DF01: order_campaign_use_amount - 完全一致
✅ IF-DW119-DF01: order_campaign - 完全一致
✅ IF-DW126-DF01: stock - 完全一致

総合結果: ✅ 全テーブル一致
比較対象テーブル数: 10
一致テーブル数: 10
差分テーブル数: 0
