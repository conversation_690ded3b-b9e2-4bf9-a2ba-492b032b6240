SELECT shop_code,
    sku_code,
    allocated_warehouse_code,
    commodity_code,
    wms_stock_quantity,
    stock_quantity,
    allocated_quantity,
    reserved_quantity,
    temporary_allocated_quantity,
    arrival_reserved_quantity,
    temporary_reserved_quantity,
    reservation_limit,
    stock_threshold,
    TO_CHAR(stock_arrival_date, 'YYYY-MM-DD') as stock_arrival_date,
    arrival_quantity,
    orm_rowid,
    created_user,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime,
    updated_user,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime
FROM stock_view
WHERE case
        when to_timestamp('1900/01/01 00:00:00', 'YYYY/MM/DD HH24:MI:SS') <> :sync_datetime then updated_datetime >= date_trunc('day', :diff_base_timestamp - INTERVAL '2 day')
        and updated_datetime <= :diff_base_timestamp
        else true
    end;
