SELECT
    category_code ,
    category_name_pc ,
    category_name_sp ,
    parent_category_code ,
    path ,
    depth ,
    display_order ,
    commodity_count ,
    TO_CHAR(last_related_count_datetime,'YYYY-MM-DD HH24:MI:SS') as last_related_count_datetime ,
    public_commodity_count ,
    TO_CHAR(last_public_count_datetime,'YYYY-MM-DD HH24:MI:SS') as last_public_count_datetime ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
    delete_flg
FROM
    category
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;