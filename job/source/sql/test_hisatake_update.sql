INSERT INTO dlpf.test_hisatake (
    customer_id,
    last_name,
    first_name,
    email,
    phone_number,
    birth_date,
    gender,
    total_purchase,
    point_balance,
    rank,
    registered_date,
    last_login,
    is_active,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    data_version
)
SELECT
    customer_id,
    last_name,
    first_name,
    email,
    phone_number,
    birth_date,
    gender,
    total_purchase,
    point_balance,
    rank,
    registered_date,
    last_login,
    is_active,
    created_user,
    created_datetime,
    'BATCH_USER' AS updated_user,
    NOW() AS updated_datetime,
    1 AS data_version
FROM dlpf.test_hisatake_work
ON CONFLICT (customer_id)
DO UPDATE SET
    last_name = EXCLUDED.last_name,
    first_name = EXCLUDED.first_name,
    email = EXCLUDED.email,
    phone_number = EXCLUDED.phone_number,
    birth_date = EXCLUDED.birth_date,
    gender = EXCLUDED.gender,
    total_purchase = EXCLUDED.total_purchase,
    point_balance = EXCLUDED.point_balance,
    rank = EXCLUDED.rank,
    registered_date = EXCLUDED.registered_date,
    last_login = EXCLUDED.last_login,
    is_active = EXCLUDED.is_active,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = 'BATCH_USER',
    updated_datetime = NOW(),
    data_version = dlpf.test_hisatake.data_version + 1;