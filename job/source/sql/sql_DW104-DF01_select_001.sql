SELECT
    customer_code ,
    credit_card_kanri_no ,
    credit_card_kanri_detail_no ,
    credit_card_no ,
    card_expire_year ,
    card_expire_month ,
    card_holder ,
    card_brand ,
    default_use_flag ,
    TO_CHAR(change_datetime,'YYYY-MM-DD HH24:MI:SS') as change_datetime ,
    change_channel_kbn ,
    card_keep_ng_flg ,
    change_reason_kbn ,
    change_reason ,
    change_user_code ,
    dhc_card_flg ,
    crm_card_id ,
    TO_CHAR(NULLIF(crm_card_updated_datetime, '')::TIMESTAMPTZ AT TIME ZONE 'JST','YYYY-MM-DD HH24:MI:SS') as crm_card_updated_datetime ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    card_info_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;