SELECT
    shop_code ,
    category_code ,
    commodity_code ,
    category_search_path ,
    search_category_code0 ,
    search_category_code1 ,
    search_category_code2 ,
    search_category_code3 ,
    search_category_code4 ,
    search_category_code5 ,
    search_category_code6 ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
,
FROM
    category_commodity
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;