TRUNCATE TABLE wk_cp001_df01_static_customer_groups_campaign_main;
TRUNCATE TABLE wk_cp001_df01_static_customer_groups_main;
-- 最初に静的顧客のキャンペーン基本情報の出力テーブルに更新されたキャンペーンのキャンペーンコードを削除扱いで入れる
-- 連携対象のものはあとからUPDATEする
INSERT INTO wk_cp001_df01_static_customer_groups_campaign_main (group_id, campaign_instructions_name, static_customer_exist_flg)
SELECT
  'txGroupId' || campaign_instructions_code
  ,'deleted_campaign'
  ,'0'
FROM wk_cp001_df01_updated_campaign;
--静的顧客のキャンペーン基本情報メインクエリ
UPDATE wk_cp001_df01_static_customer_groups_campaign_main AS dscg
SET
  campaign_instructions_name = ci.campaign_instructions_name
  ,static_customer_exist_flg = uecl.static_customer_exist_flg
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
  uecl.static_customer_flg = '1'
  AND uecl.campaign_instructions_code = ci.campaign_instructions_code
WHERE dscg.group_id = 'txGroupId' || uecl.campaign_instructions_code;
--静的顧客の顧客グループ情報メインクエリ
INSERT INTO wk_cp001_df01_static_customer_groups_main (group_id, customer_no)
SELECT 
  'txGroupId' || uecl.campaign_instructions_code
  ,c.customer_no
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
  uecl.static_customer_flg = '1'
  AND uecl.static_customer_exist_flg = '1'
  AND uecl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_customer_view AS cc ON
  ci.campaign_instructions_code = cc.campaign_instructions_code
  AND cc.joken_type = '1'
  AND cc.delete_flg = 0
INNER JOIN customer AS c ON
  cc.customer_code = c.customer_code;
--分割単位の設定
-- 静的顧客(キャンペーン基本情報)
WITH temp_parallel_num AS (
  SELECT
     group_id
    ,MOD(ROW_NUMBER() OVER (ORDER BY group_id) - 1, :parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT group_id FROM wk_cp001_df01_static_customer_groups_campaign_main)
)
UPDATE wk_cp001_df01_static_customer_groups_campaign_main AS scgcm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE scgcm.group_id = t.group_id;
-- 静的顧客(キャンペーン顧客グループ情報)
UPDATE wk_cp001_df01_static_customer_groups_main AS scgm
SET split_num = scgcm.split_num
FROM wk_cp001_df01_static_customer_groups_campaign_main AS scgcm
WHERE scgcm.group_id = scgm.group_id;