SELECT order_no,
    tax_group_code,
    tax_no,
    use_code_type,
    use_code,
    use_amount,
    tax_rate,
    orm_rowid,
    created_user,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime,
    updated_user,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime,
    delete_flg
FROM order_campaign_use_amount_view
WHERE case
        when to_timestamp('1900/01/01 00:00:00', 'YYYY/MM/DD HH24:MI:SS') <> :sync_datetime then updated_datetime >= date_trunc('day', :diff_base_timestamp - INTERVAL '2 day')
        and updated_datetime <= :diff_base_timestamp
        else true
    end;
