#!/usr/bin/env python3
"""
OMSのDDLと開発環境のDBスキーマ（oms_readreplica）のテーブル構造比較スクリプト
"""

import psycopg2
import re
import os
from typing import Dict, List, Tuple

# 比較対象テーブルの定義
TARGET_TABLES = {
    "IF-DW102-DF01": {"name": "category", "description": "カテゴリ"},
    "IF-DW103-DF01": {"name": "category_commodity", "description": "カテゴリ陳列商品"},
    "IF-DW104-DF01": {"name": "card_info", "description": "カード情報"},
    "IF-DW106-DF01": {"name": "campaign_customer", "description": "キャンペーン設定顧客"},
    "IF-DW108-DF01": {"name": "coupon_customer", "description": "クーポン顧客関連付け"},
    "IF-DW116-DF01": {"name": "shipping_detail", "description": "出荷明細"},
    "IF-DW117-DF01": {"name": "shipping_detail_composition", "description": "出荷明細構成品"},
    "IF-DW118-DF01": {"name": "order_campaign_use_amount", "description": "受注キャンペーンクーポン利用額"},
    "IF-DW119-DF01": {"name": "order_campaign", "description": "受注キャンペーン適用"},
    "IF-DW126-DF01": {"name": "stock", "description": "在庫"}
}

# DB接続設定
DB_CONFIG = {
    "host": "localhost",
    "port": "15432",
    "database": "dlpf-dev",
    "user": "webshop",
    "password": "password"
}

def get_db_table_structure(table_name: str) -> List[Dict]:
    """開発環境DBからテーブル構造を取得"""
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # テーブル構造を取得
    cursor.execute(f"""
        SELECT
            column_name,
            data_type,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            is_nullable,
            column_default
        FROM information_schema.columns
        WHERE table_schema = 'oms_readreplica'
        AND table_name = '{table_name}'
        ORDER BY ordinal_position
    """)

    columns = []
    for row in cursor.fetchall():
        col_name, data_type, char_len, num_prec, num_scale, nullable, default = row

        # データ型の正規化
        if data_type == 'character varying':
            type_str = f"varchar({char_len})"
        elif data_type == 'numeric':
            if num_scale and num_scale > 0:
                type_str = f"numeric({num_prec},{num_scale})"
            else:
                type_str = f"numeric({num_prec},0)"
        elif data_type == 'timestamp without time zone':
            # precision情報を取得
            cursor.execute(f"""
                SELECT datetime_precision
                FROM information_schema.columns
                WHERE table_schema = 'oms_readreplica'
                AND table_name = '{table_name}'
                AND column_name = '{col_name}'
            """)
            precision = cursor.fetchone()[0]
            type_str = f"timestamp({precision})"
        else:
            type_str = data_type

        columns.append({
            "name": col_name,
            "type": type_str,
            "nullable": nullable == "YES",
            "default": default
        })

    cursor.close()
    conn.close()
    return columns

def parse_ddl_file(file_path: str) -> List[Dict]:
    """DDLファイルからテーブル構造を解析"""
    if not os.path.exists(file_path):
        return []

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    columns = []

    # CREATE TABLE文の開始を見つける
    create_start = content.lower().find('create table')
    if create_start == -1:
        return []

    # 括弧の開始を見つける
    paren_start = content.find('(', create_start)
    if paren_start == -1:
        return []

    # 対応する括弧の終了を見つける
    paren_count = 0
    paren_end = -1
    for i in range(paren_start, len(content)):
        if content[i] == '(':
            paren_count += 1
        elif content[i] == ')':
            paren_count -= 1
            if paren_count == 0:
                paren_end = i
                break

    if paren_end == -1:
        return []

    # テーブル定義部分を抽出
    table_def = content[paren_start + 1:paren_end]

    # 各列の定義を解析
    lines = table_def.split('\n')
    for line in lines:
        line = line.strip()
        if (not line or
            line.startswith('constraint') or
            line.startswith('--') or
            line.startswith('tablespace') or
            'using index' in line.lower() or
            'tablespace' in line.lower()):
            continue

        # 末尾のカンマを除去
        line = line.rstrip(',')

        # 列定義の解析
        # パターン: column_name type [not null]
        parts = line.split()
        if len(parts) >= 2 and not parts[0].startswith('constraint'):
            col_name = parts[0]
            col_type = parts[1]

            # NOT NULL制約の確認
            nullable = 'not null' not in line.lower()

            columns.append({
                "name": col_name,
                "type": col_type,
                "nullable": nullable,
                "default": None
            })

    return columns

def compare_table_structures(db_columns: List[Dict], ddl_columns: List[Dict]) -> Dict:
    """テーブル構造の比較"""
    result = {
        "match": True,
        "differences": [],
        "db_only": [],
        "ddl_only": []
    }

    db_col_dict = {col["name"]: col for col in db_columns}
    ddl_col_dict = {col["name"]: col for col in ddl_columns}

    # DB側にのみ存在する列
    for col_name in db_col_dict:
        if col_name not in ddl_col_dict:
            result["db_only"].append(col_name)
            result["match"] = False

    # DDL側にのみ存在する列
    for col_name in ddl_col_dict:
        if col_name not in db_col_dict:
            result["ddl_only"].append(col_name)
            result["match"] = False

    # 共通列の詳細比較
    for col_name in db_col_dict:
        if col_name in ddl_col_dict:
            db_col = db_col_dict[col_name]
            ddl_col = ddl_col_dict[col_name]

            differences = []
            if db_col["type"] != ddl_col["type"]:
                differences.append(f"型: DB={db_col['type']} vs DDL={ddl_col['type']}")
            if db_col["nullable"] != ddl_col["nullable"]:
                differences.append(f"NULL許可: DB={db_col['nullable']} vs DDL={ddl_col['nullable']}")

            if differences:
                result["differences"].append({
                    "column": col_name,
                    "issues": differences
                })
                result["match"] = False

    return result

def main():
    """メイン処理"""
    print("=== OMSテーブル構造比較レポート ===\n")

    all_match = True
    summary_results = []

    for if_code, table_info in TARGET_TABLES.items():
        table_name = table_info["name"]
        description = table_info["description"]
        ddl_file = f"oms_tabledef/{table_name}.sql"

        print(f"## {if_code}: {table_name} ({description})")
        print("-" * 60)

        try:
            # DB構造取得
            db_columns = get_db_table_structure(table_name)
            print(f"DB側列数: {len(db_columns)}")

            # DDL構造取得
            ddl_columns = parse_ddl_file(ddl_file)
            print(f"DDL側列数: {len(ddl_columns)}")

            # 比較実行
            comparison = compare_table_structures(db_columns, ddl_columns)

            if comparison["match"]:
                print("✅ **完全一致**")
                summary_results.append(f"✅ {if_code}: {table_name} - 完全一致")
            else:
                print("❌ **差分あり**")
                all_match = False
                summary_results.append(f"❌ {if_code}: {table_name} - 差分あり")

                if comparison["db_only"]:
                    print(f"  - DB側のみ: {', '.join(comparison['db_only'])}")

                if comparison["ddl_only"]:
                    print(f"  - DDL側のみ: {', '.join(comparison['ddl_only'])}")

                if comparison["differences"]:
                    print("  - 列定義差分:")
                    for diff in comparison["differences"]:
                        print(f"    * {diff['column']}: {'; '.join(diff['issues'])}")

        except Exception as e:
            print(f"❌ **エラー**: {str(e)}")
            all_match = False
            summary_results.append(f"❌ {if_code}: {table_name} - エラー")

        print()

    # サマリー出力
    print("=" * 60)
    print("=== 比較結果サマリー ===")
    print("=" * 60)
    for result in summary_results:
        print(result)

    print(f"\n総合結果: {'✅ 全テーブル一致' if all_match else '❌ 差分またはエラーあり'}")
    print(f"比較対象テーブル数: {len(TARGET_TABLES)}")
    print(f"一致テーブル数: {len([r for r in summary_results if '✅' in r])}")
    print(f"差分テーブル数: {len([r for r in summary_results if '❌' in r])}")

if __name__ == "__main__":
    main()
